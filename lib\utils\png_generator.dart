import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';

/// Generate PNG coloring book images
class PngGenerator {
  
  /// Generate a dragon coloring PNG
  static Future<Uint8List> generateDragonPng() async {
    const int width = 400;
    const int height = 500;
    
    final ui.PictureRecorder recorder = ui.PictureRecorder();
    final Canvas canvas = Canvas(recorder);
    
    // White background
    canvas.drawRect(
      Rect.fromLTWH(0, 0, width.toDouble(), height.toDouble()),
      Paint()..color = Colors.white,
    );
    
    final Paint linePaint = Paint()
      ..color = Colors.black
      ..strokeWidth = 4
      ..style = PaintingStyle.stroke;
    
    // Dragon head
    canvas.drawCircle(const Offset(200, 150), 60, linePaint);
    
    // Dragon body
    canvas.drawOval(const Rect.fromLTWH(150, 200, 100, 150), linePaint);
    
    // Left wing
    final Path leftWing = Path()
      ..moveTo(150, 220)
      ..quadraticBezierTo(80, 200, 100, 280)
      ..quadraticBezierTo(120, 320, 150, 300)
      ..close();
    canvas.drawPath(leftWing, linePaint);
    
    // Right wing
    final Path rightWing = Path()
      ..moveTo(250, 220)
      ..quadraticBezierTo(320, 200, 300, 280)
      ..quadraticBezierTo(280, 320, 250, 300)
      ..close();
    canvas.drawPath(rightWing, linePaint);
    
    // Tail
    final Path tail = Path()
      ..moveTo(250, 330)
      ..quadraticBezierTo(300, 350, 280, 400)
      ..quadraticBezierTo(260, 420, 240, 380)
      ..close();
    canvas.drawPath(tail, linePaint);
    
    // Legs
    canvas.drawOval(const Rect.fromLTWH(160, 340, 30, 60), linePaint);
    canvas.drawOval(const Rect.fromLTWH(210, 340, 30, 60), linePaint);
    
    // Eyes
    canvas.drawCircle(const Offset(185, 140), 10, linePaint);
    canvas.drawCircle(const Offset(215, 140), 10, linePaint);
    
    // Convert to PNG
    final ui.Picture picture = recorder.endRecording();
    final ui.Image image = await picture.toImage(width, height);
    final ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    
    return byteData!.buffer.asUint8List();
  }
  
  /// Generate an alien coloring PNG
  static Future<Uint8List> generateAlienPng() async {
    const int width = 400;
    const int height = 500;
    
    final ui.PictureRecorder recorder = ui.PictureRecorder();
    final Canvas canvas = Canvas(recorder);
    
    // White background
    canvas.drawRect(
      Rect.fromLTWH(0, 0, width.toDouble(), height.toDouble()),
      Paint()..color = Colors.white,
    );
    
    final Paint linePaint = Paint()
      ..color = Colors.black
      ..strokeWidth = 4
      ..style = PaintingStyle.stroke;
    
    // Alien head
    canvas.drawOval(const Rect.fromLTWH(150, 100, 100, 120), linePaint);
    
    // Antennae
    canvas.drawLine(const Offset(170, 100), const Offset(160, 60), linePaint);
    canvas.drawCircle(const Offset(160, 60), 10, linePaint);
    canvas.drawLine(const Offset(230, 100), const Offset(240, 60), linePaint);
    canvas.drawCircle(const Offset(240, 60), 10, linePaint);
    
    // Body
    canvas.drawOval(const Rect.fromLTWH(160, 210, 80, 120), linePaint);
    
    // Arms
    canvas.drawOval(const Rect.fromLTWH(100, 230, 70, 30), linePaint);
    canvas.drawOval(const Rect.fromLTWH(230, 230, 70, 30), linePaint);
    
    // Hands
    canvas.drawCircle(const Offset(110, 245), 15, linePaint);
    canvas.drawCircle(const Offset(290, 245), 15, linePaint);
    
    // Legs
    canvas.drawOval(const Rect.fromLTWH(170, 320, 25, 80), linePaint);
    canvas.drawOval(const Rect.fromLTWH(205, 320, 25, 80), linePaint);
    
    // Eyes
    canvas.drawCircle(const Offset(180, 150), 18, linePaint);
    canvas.drawCircle(const Offset(220, 150), 18, linePaint);
    
    // Convert to PNG
    final ui.Picture picture = recorder.endRecording();
    final ui.Image image = await picture.toImage(width, height);
    final ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    
    return byteData!.buffer.asUint8List();
  }
  
  /// Generate a flower coloring PNG
  static Future<Uint8List> generateFlowerPng() async {
    const int width = 400;
    const int height = 500;
    
    final ui.PictureRecorder recorder = ui.PictureRecorder();
    final Canvas canvas = Canvas(recorder);
    
    // White background
    canvas.drawRect(
      Rect.fromLTWH(0, 0, width.toDouble(), height.toDouble()),
      Paint()..color = Colors.white,
    );
    
    final Paint linePaint = Paint()
      ..color = Colors.black
      ..strokeWidth = 4
      ..style = PaintingStyle.stroke;
    
    // Flower center
    canvas.drawCircle(const Offset(200, 200), 30, linePaint);
    
    // Petals (6 petals)
    final List<Offset> petalCenters = [
      const Offset(200, 140), // Top
      const Offset(252, 170), // Top right
      const Offset(252, 230), // Bottom right
      const Offset(200, 260), // Bottom
      const Offset(148, 230), // Bottom left
      const Offset(148, 170), // Top left
    ];
    
    for (final center in petalCenters) {
      canvas.drawOval(
        Rect.fromCenter(center: center, width: 50, height: 80),
        linePaint,
      );
    }
    
    // Stem
    canvas.drawRect(const Rect.fromLTWH(190, 260, 20, 140), linePaint);
    
    // Leaves
    final Path leftLeaf = Path()
      ..moveTo(190, 320)
      ..quadraticBezierTo(140, 300, 150, 340)
      ..quadraticBezierTo(170, 360, 190, 340)
      ..close();
    canvas.drawPath(leftLeaf, linePaint);
    
    final Path rightLeaf = Path()
      ..moveTo(210, 360)
      ..quadraticBezierTo(260, 340, 250, 380)
      ..quadraticBezierTo(230, 400, 210, 380)
      ..close();
    canvas.drawPath(rightLeaf, linePaint);
    
    // Convert to PNG
    final ui.Picture picture = recorder.endRecording();
    final ui.Image image = await picture.toImage(width, height);
    final ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    
    return byteData!.buffer.asUint8List();
  }
}
