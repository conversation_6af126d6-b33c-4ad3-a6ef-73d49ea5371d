import 'package:flutter/material.dart';
import 'dart:math' as math;

/// Animated brush widget for processing indicator
class AnimatedBrush extends StatefulWidget {
  const AnimatedBrush({
    super.key,
    this.size = 48.0,
    this.color = Colors.white,
  });

  final double size;
  final Color color;

  @override
  State<AnimatedBrush> createState() => _AnimatedBrushState();
}

class _AnimatedBrushState extends State<AnimatedBrush>
    with TickerProviderStateMixin {
  late AnimationController _paintingController;
  late Animation<double> _paintingAnimation;

  @override
  void initState() {
    super.initState();

    // Painting motion animation - back and forth like painting
    _paintingController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _paintingAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _paintingController,
      curve: Curves.easeInOut,
    ));

    // Start painting motion
    _paintingController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _paintingController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: AnimatedBuilder(
        animation: _paintingAnimation,
        builder: (context, child) {
          return CustomPaint(
            size: Size(widget.size, widget.size),
            painter: BrushPainter(
              color: widget.color,
              paintProgress: _paintingAnimation.value,
            ),
          );
        },
      ),
    );
  }
}

/// Brush painter that matches the reference GIF exactly
class BrushPainter extends CustomPainter {
  final Color color;
  final double paintProgress;

  BrushPainter({
    required this.color,
    required this.paintProgress,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;

    // Save canvas state
    canvas.save();

    // Rotate canvas to angle the brush diagonally (like in reference)
    canvas.translate(center.dx, center.dy);
    canvas.rotate(-0.6); // Angled like in the GIF

    // Add painting motion - slight back and forth movement
    final paintOffset = math.sin(paintProgress * 2 * 3.14159) * radius * 0.1;
    canvas.translate(paintOffset, 0);

    // Wooden handle (orange/brown like reference)
    final handlePaint = Paint()
      ..color = const Color(0xFFCD853F) // Sandy brown like reference
      ..style = PaintingStyle.fill;

    final handlePath = Path();
    handlePath.moveTo(-radius * 0.12, radius * 0.6);
    handlePath.lineTo(radius * 0.12, radius * 0.6);
    handlePath.lineTo(radius * 0.08, -radius * 0.2);
    handlePath.lineTo(-radius * 0.08, -radius * 0.2);
    handlePath.close();
    canvas.drawPath(handlePath, handlePaint);

    // Black outline for handle
    final outlinePaint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;
    canvas.drawPath(handlePath, outlinePaint);

    // Metal ferrule (gray like reference)
    final ferrulePaint = Paint()
      ..color = const Color(0xFFA9A9A9) // Dark gray
      ..style = PaintingStyle.fill;

    final ferrulePath = Path();
    ferrulePath.moveTo(-radius * 0.08, -radius * 0.2);
    ferrulePath.lineTo(radius * 0.08, -radius * 0.2);
    ferrulePath.lineTo(radius * 0.06, -radius * 0.4);
    ferrulePath.lineTo(-radius * 0.06, -radius * 0.4);
    ferrulePath.close();
    canvas.drawPath(ferrulePath, ferrulePaint);

    // Black outline for ferrule
    canvas.drawPath(ferrulePath, outlinePaint);

    // Brush tip (dark brown/black like reference)
    final tipPaint = Paint()
      ..color = const Color(0xFF8B4513) // Saddle brown
      ..style = PaintingStyle.fill;

    final tipPath = Path();
    tipPath.moveTo(-radius * 0.06, -radius * 0.4);
    tipPath.lineTo(radius * 0.06, -radius * 0.4);
    tipPath.lineTo(0, -radius * 0.7);
    tipPath.close();
    canvas.drawPath(tipPath, tipPaint);

    // Black outline for tip
    canvas.drawPath(tipPath, outlinePaint);

    // Restore canvas
    canvas.restore();

    // Paint splash at the bottom (cyan like reference)
    final splashPaint = Paint()
      ..color = const Color(0xFF40E0D0) // Turquoise like reference
      ..style = PaintingStyle.fill;

    // Create paint splash shape at bottom
    final splashY = center.dy + radius * 0.6;
    final splashWidth = radius * (0.8 + 0.2 * math.sin(paintProgress * 3.14159));

    // Draw irregular paint splash
    final splashPath = Path();
    for (int i = 0; i < 20; i++) {
      final angle = i * 2 * 3.14159 / 20;
      final distance = splashWidth * (0.3 + 0.2 * math.sin(i * 0.8 + paintProgress * 6.28));
      final x = center.dx + distance * math.cos(angle);
      final y = splashY + distance * 0.3 * math.sin(angle);

      if (i == 0) {
        splashPath.moveTo(x, y);
      } else {
        splashPath.lineTo(x, y);
      }
    }
    splashPath.close();
    canvas.drawPath(splashPath, splashPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
