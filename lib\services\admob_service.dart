import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';

/// AdMob Service for managing all ad types in the app
class AdMobService {
  static final AdMobService _instance = AdMobService._internal();
  factory AdMobService() => _instance;
  AdMobService._internal();

  static AdMobService get instance => _instance;

  // Ad state tracking
  bool _isInitialized = false;
  BannerAd? _bannerAd;
  InterstitialAd? _interstitialAd;
  RewardedAd? _rewardedAd;
  
  // Ad loading states
  bool _isBannerLoaded = false;
  bool _isInterstitialLoaded = false;
  bool _isRewardedLoaded = false;
  
  // Ad frequency control
  DateTime? _lastInterstitialShown;
  int _interstitialCooldownMinutes = 10; // Show interstitial max every 10 minutes
  int _actionCounter = 0; // Track user actions
  int _actionsBeforeInterstitial = 4; // Show interstitial after every 4 actions
  
  // Test Ad Unit IDs (AdMob Test IDs)
  static const String _testBannerAdUnitId = 'ca-app-pub-3940256099942544/6300978111';
  static const String _testInterstitialAdUnitId = 'ca-app-pub-3940256099942544/1033173712';
  static const String _testRewardedAdUnitId = 'ca-app-pub-3940256099942544/5224354917';

  /// Initialize AdMob SDK
  Future<void> initialize() async {
    if (_isInitialized) {
      if (kDebugMode) print('🎯 AdMob already initialized');
      return;
    }

    try {
      if (kDebugMode) print('🎯 Starting AdMob initialization...');

      // Configure test device settings for better ad fill
      if (kDebugMode) {
        final requestConfiguration = RequestConfiguration(
          testDeviceIds: ['33BE2250B43518CCDA7DE426D04EE231'], // Add your test device ID here
        );
        MobileAds.instance.updateRequestConfiguration(requestConfiguration);
        print('🧪 Test device configuration applied');
      }

      final initializationStatus = await MobileAds.instance.initialize();
      _isInitialized = true;

      if (kDebugMode) {
        print('✅ AdMob initialized successfully');
        print('📊 Initialization status: ${initializationStatus.adapterStatuses}');
      }

      // Wait a moment before loading ads to ensure proper initialization
      await Future.delayed(const Duration(milliseconds: 500));

      // Load initial ads
      _loadBannerAd();
      _loadInterstitialAd();
      _loadRewardedAd();

    } catch (e) {
      if (kDebugMode) {
        print('❌ AdMob initialization failed: $e');
      }
    }
  }

  /// Get banner ad unit ID based on platform
  String get _bannerAdUnitId {
    if (kDebugMode) return _testBannerAdUnitId;
    
    if (Platform.isAndroid) {
      return 'ca-app-pub-XXXXXXXXXXXXXXXX/XXXXXXXXXX'; // Replace with your Android banner ID
    } else if (Platform.isIOS) {
      return 'ca-app-pub-XXXXXXXXXXXXXXXX/XXXXXXXXXX'; // Replace with your iOS banner ID
    }
    return _testBannerAdUnitId;
  }

  /// Get interstitial ad unit ID based on platform
  String get _interstitialAdUnitId {
    if (kDebugMode) return _testInterstitialAdUnitId;
    
    if (Platform.isAndroid) {
      return 'ca-app-pub-XXXXXXXXXXXXXXXX/XXXXXXXXXX'; // Replace with your Android interstitial ID
    } else if (Platform.isIOS) {
      return 'ca-app-pub-XXXXXXXXXXXXXXXX/XXXXXXXXXX'; // Replace with your iOS interstitial ID
    }
    return _testInterstitialAdUnitId;
  }

  /// Get rewarded ad unit ID based on platform
  String get _rewardedAdUnitId {
    if (kDebugMode) return _testRewardedAdUnitId;
    
    if (Platform.isAndroid) {
      return 'ca-app-pub-XXXXXXXXXXXXXXXX/XXXXXXXXXX'; // Replace with your Android rewarded ID
    } else if (Platform.isIOS) {
      return 'ca-app-pub-XXXXXXXXXXXXXXXX/XXXXXXXXXX'; // Replace with your iOS rewarded ID
    }
    return _testRewardedAdUnitId;
  }

  /// Create a new banner ad instance (for unique widgets)
  BannerAd? createBannerAd({String? context}) {
    if (!_isInitialized) {
      if (kDebugMode) print('❌ AdMob not initialized, cannot create banner ad');
      return null;
    }

    if (kDebugMode) print('🎯 Creating banner ad for context: $context');

    return BannerAd(
      adUnitId: _bannerAdUnitId,
      size: AdSize.banner,
      request: const AdRequest(
        keywords: ['game', 'coloring', 'art', 'creative'],
        nonPersonalizedAds: false,
      ),
      listener: BannerAdListener(
        onAdLoaded: (ad) {
          if (kDebugMode) print('✅ Banner ad loaded for: $context');
        },
        onAdFailedToLoad: (ad, error) {
          ad.dispose();
          if (kDebugMode) {
            print('❌ Banner ad failed to load for $context: ${error.message}');
            print('❌ Error code: ${error.code}');
          }
        },
        onAdOpened: (ad) {
          if (kDebugMode) print('🎯 Banner ad opened for: $context');
        },
        onAdClosed: (ad) {
          if (kDebugMode) print('🎯 Banner ad closed for: $context');
        },
        onAdImpression: (ad) {
          if (kDebugMode) print('👁️ Banner ad impression for: $context');
        },
      ),
    );
  }

  /// Load banner ad (legacy method for backward compatibility)
  void _loadBannerAd() {
    if (kDebugMode) print('🎯 Loading banner ad with ID: $_bannerAdUnitId');

    _bannerAd = createBannerAd(context: 'legacy');
    _bannerAd?.load();
  }

  /// Load interstitial ad
  void _loadInterstitialAd() {
    InterstitialAd.load(
      adUnitId: _interstitialAdUnitId,
      request: const AdRequest(
        keywords: ['game', 'coloring', 'art', 'creative'],
        nonPersonalizedAds: false,
      ),
      adLoadCallback: InterstitialAdLoadCallback(
        onAdLoaded: (ad) {
          _interstitialAd = ad;
          _isInterstitialLoaded = true;
          if (kDebugMode) print('🎯 Interstitial ad loaded');
          
          _interstitialAd!.setImmersiveMode(true);
        },
        onAdFailedToLoad: (error) {
          _isInterstitialLoaded = false;
          if (kDebugMode) print('❌ Interstitial ad failed to load: $error');
          
          // Retry loading after delay
          Future.delayed(const Duration(seconds: 30), () {
            _loadInterstitialAd();
          });
        },
      ),
    );
  }

  /// Load rewarded ad
  void _loadRewardedAd() {
    RewardedAd.load(
      adUnitId: _rewardedAdUnitId,
      request: const AdRequest(
        keywords: ['game', 'coloring', 'art', 'creative'],
        nonPersonalizedAds: false,
      ),
      rewardedAdLoadCallback: RewardedAdLoadCallback(
        onAdLoaded: (ad) {
          _rewardedAd = ad;
          _isRewardedLoaded = true;
          if (kDebugMode) print('🎯 Rewarded ad loaded');
        },
        onAdFailedToLoad: (error) {
          _isRewardedLoaded = false;
          if (kDebugMode) print('❌ Rewarded ad failed to load: $error');
          
          // Retry loading after delay
          Future.delayed(const Duration(seconds: 30), () {
            _loadRewardedAd();
          });
        },
      ),
    );
  }

  /// Get banner ad widget
  BannerAd? get bannerAd => _isBannerLoaded ? _bannerAd : null;

  /// Check if banner ad is ready
  bool get isBannerReady => _isBannerLoaded && _bannerAd != null;

  /// Show interstitial ad with frequency control
  Future<bool> showInterstitialAd({String? context}) async {
    if (!_isInterstitialLoaded || _interstitialAd == null) {
      if (kDebugMode) print('❌ Interstitial ad not ready');
      return false;
    }

    // Increment action counter
    _actionCounter++;

    // Check action counter - only show ad after certain number of actions
    if (_actionCounter < _actionsBeforeInterstitial) {
      if (kDebugMode) print('🔢 Interstitial ad skipped - action count: $_actionCounter/$_actionsBeforeInterstitial');
      return false;
    }

    // Check cooldown period
    if (_lastInterstitialShown != null) {
      final timeSinceLastAd = DateTime.now().difference(_lastInterstitialShown!);
      if (timeSinceLastAd.inMinutes < _interstitialCooldownMinutes) {
        if (kDebugMode) print('🕒 Interstitial ad on cooldown');
        return false;
      }
    }

    try {
      bool adCompleted = false;
      
      _interstitialAd!.fullScreenContentCallback = FullScreenContentCallback(
        onAdShowedFullScreenContent: (ad) {
          if (kDebugMode) print('🎯 Interstitial ad showed: ${context ?? "unknown"}');
        },
        onAdDismissedFullScreenContent: (ad) {
          adCompleted = true;
          ad.dispose();
          _isInterstitialLoaded = false;
          _lastInterstitialShown = DateTime.now();
          _actionCounter = 0; // Reset action counter after showing ad

          // Load next interstitial
          _loadInterstitialAd();

          if (kDebugMode) print('🎯 Interstitial ad dismissed');
        },
        onAdFailedToShowFullScreenContent: (ad, error) {
          ad.dispose();
          _isInterstitialLoaded = false;
          _loadInterstitialAd();
          
          if (kDebugMode) print('❌ Interstitial ad failed to show: $error');
        },
      );

      await _interstitialAd!.show();
      return true;
    } catch (e) {
      if (kDebugMode) print('❌ Error showing interstitial ad: $e');
      return false;
    }
  }

  /// Show rewarded ad
  Future<bool> showRewardedAd({
    required Function() onRewardEarned,
    Function()? onAdClosed,
    String? context,
  }) async {
    if (!_isRewardedLoaded || _rewardedAd == null) {
      if (kDebugMode) print('❌ Rewarded ad not ready');
      return false;
    }

    try {
      bool rewardEarned = false;
      
      _rewardedAd!.fullScreenContentCallback = FullScreenContentCallback(
        onAdShowedFullScreenContent: (ad) {
          if (kDebugMode) print('🎯 Rewarded ad showed: ${context ?? "unknown"}');
        },
        onAdDismissedFullScreenContent: (ad) {
          ad.dispose();
          _isRewardedLoaded = false;
          
          // Load next rewarded ad
          _loadRewardedAd();
          
          onAdClosed?.call();
          if (kDebugMode) print('🎯 Rewarded ad dismissed');
        },
        onAdFailedToShowFullScreenContent: (ad, error) {
          ad.dispose();
          _isRewardedLoaded = false;
          _loadRewardedAd();
          
          onAdClosed?.call();
          if (kDebugMode) print('❌ Rewarded ad failed to show: $error');
        },
      );

      await _rewardedAd!.show(
        onUserEarnedReward: (ad, reward) {
          rewardEarned = true;
          onRewardEarned();
          if (kDebugMode) print('🎁 Reward earned: ${reward.amount} ${reward.type}');
        },
      );
      
      return rewardEarned;
    } catch (e) {
      if (kDebugMode) print('❌ Error showing rewarded ad: $e');
      return false;
    }
  }

  /// Check if interstitial ad is ready
  bool get isInterstitialReady => _isInterstitialLoaded && _interstitialAd != null;

  /// Check if rewarded ad is ready
  bool get isRewardedReady => _isRewardedLoaded && _rewardedAd != null;

  /// Dispose all ads
  void dispose() {
    _bannerAd?.dispose();
    _interstitialAd?.dispose();
    _rewardedAd?.dispose();
    
    _bannerAd = null;
    _interstitialAd = null;
    _rewardedAd = null;
    
    _isBannerLoaded = false;
    _isInterstitialLoaded = false;
    _isRewardedLoaded = false;
  }

  /// Set interstitial cooldown period (in minutes)
  void setInterstitialCooldown(int minutes) {
    _interstitialCooldownMinutes = minutes;
  }

  /// Set how many actions before showing interstitial ad
  void setInterstitialActionFrequency(int actions) {
    _actionsBeforeInterstitial = actions;
  }

  /// Get current interstitial settings
  Map<String, int> getInterstitialSettings() {
    return {
      'cooldownMinutes': _interstitialCooldownMinutes,
      'actionsBeforeAd': _actionsBeforeInterstitial,
      'currentActionCount': _actionCounter,
    };
  }

  /// Force reload all ads
  void reloadAllAds() {
    if (_isInitialized) {
      _loadBannerAd();
      _loadInterstitialAd();
      _loadRewardedAd();
    }
  }

  /// Debug method to check ad status
  void debugAdStatus() {
    if (kDebugMode) {
      print('🔍 === AD STATUS DEBUG ===');
      print('🔍 Initialized: $_isInitialized');
      print('🔍 Banner loaded: $_isBannerLoaded');
      print('🔍 Banner ad: ${_bannerAd != null}');
      print('🔍 Interstitial loaded: $_isInterstitialLoaded');
      print('🔍 Rewarded loaded: $_isRewardedLoaded');
      print('🔍 Banner ad unit ID: $_bannerAdUnitId');
      print('🔍 ========================');
    }
  }
}
