import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'canvas_coloring_screen.dart';
import '../services/asset_service.dart';
import '../services/reward_service.dart';
import '../services/ad_test_service.dart';
import '../services/admob_service.dart';
import '../widgets/banner_ad_widget.dart';

class CategoryScreen extends StatefulWidget {
  final CategoryData category;

  const CategoryScreen({
    super.key,
    required this.category,
  });

  @override
  State<CategoryScreen> createState() => _CategoryScreenState();
}

class _CategoryScreenState extends State<CategoryScreen> {
  // Images are already loaded in the CategoryData object
  List<String> _unlockedImages = [];
  bool _isLoadingUnlocks = true;

  @override
  void initState() {
    super.initState();
    _loadUnlockedImages();
  }

  /// Load unlocked images from reward service
  Future<void> _loadUnlockedImages() async {
    try {
      final rewardService = RewardService.instance;
      final unlockedImages = await rewardService.getUnlockedImages();
      if (mounted) {
        setState(() {
          _unlockedImages = unlockedImages;
          _isLoadingUnlocks = false;
        });
      }
    } catch (e) {
      print('Error loading unlocked images: $e');
      if (mounted) {
        setState(() {
          _isLoadingUnlocks = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1E1E1E),
      body: Column(
        children: [
          // Header
          _buildHeader(),

          // Back button and category title
          _buildCategoryHeader(),

          // Banner ad
          const SectionBannerAd(section: 'category_top'),

          // Images grid
          Expanded(
            child: _buildImagesGrid(),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.only(
        top: MediaQuery.of(context).padding.top + 10, // Reduced from 20 (50% less)
        bottom: 15, // Reduced from 30 (50% less)
        left: 20,
        right: 20,
      ),
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          colors: [Color(0xFFFF6B9D), Color(0xFF4ECDC4)],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 20, // Reduced from 40 (50% less)
                height: 20, // Reduced from 40 (50% less)
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(6), // Reduced from 12
                ),
                child: const Icon(
                  Icons.palette,
                  color: Colors.white,
                  size: 12, // Reduced from 24 (50% less)
                ),
              ),
              const SizedBox(width: 6), // Reduced from 12
              const Text(
                'ColorNest',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 12, // Reduced from 24 (50% less)
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 6), // Reduced from 12
          const Text(
            "Let's make beautiful colors together!",
            style: TextStyle(
              color: Colors.white,
              fontSize: 8, // Reduced from 16 (50% less)
              fontWeight: FontWeight.w400,
            ),
          ),
          const SizedBox(height: 8), // Reduced from 16
          // Color dots
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildColorDot(const Color(0xFFFF6B9D)),
              _buildColorDot(const Color(0xFFFF8A5B)),
              _buildColorDot(const Color(0xFFFFD93D)),
              _buildColorDot(const Color(0xFF6BCF7F)),
              _buildColorDot(const Color(0xFF4ECDC4)),
              _buildColorDot(const Color(0xFF45B7D1)),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildColorDot(Color color) {
    return Container(
      width: 12,
      height: 12,
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: color,
        shape: BoxShape.circle,
      ),
    );
  }

  Widget _buildCategoryHeader() {
    return Container(
      padding: const EdgeInsets.all(8), // Reduced from 20
      child: Column(
        children: [
          // Back button
          GestureDetector(
            onTap: () {
              HapticFeedback.lightImpact();
              Navigator.pop(context);
            },
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12), // Reduced from 16,20
              decoration: BoxDecoration(
                color: const Color(0xFF2A2A2A),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: const Color(0xFF404040)),
              ),
              child: const Row(
                children: [
                  Icon(
                    Icons.arrow_back,
                    color: Colors.white,
                    size: 18, // Reduced from 24
                  ),
                  SizedBox(width: 8), // Reduced from 12
                  Text(
                    'Back to Categories',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14, // Reduced from 16
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 8), // Reduced from 20

          // Category title
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16), // Reduced from 24,20
            decoration: BoxDecoration(
              color: const Color(0xFF2A2A2A),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: const Color(0xFF404040)),
            ),
            child: Column(
              children: [
                Text(
                  widget.category.displayName.toLowerCase(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20, // Reduced from 28
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4), // Reduced from 8
                Text(
                  'Choose your favorite ${widget.category.displayName.toLowerCase()} design',
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 12, // Reduced from 16
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildImagesGrid() {
    // Combine available and locked images for display
    final allImages = [...widget.category.availableImages, ...widget.category.lockedImages];

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 0.8,
        ),
        itemCount: allImages.length,
        itemBuilder: (context, index) {
          final imageName = allImages[index];
          final isLockedByCategory = widget.category.lockedImages.contains(imageName);
          final imageKey = '${widget.category.name}/$imageName';
          final isUnlockedByReward = _unlockedImages.contains(imageKey);
          final isLocked = isLockedByCategory && !isUnlockedByReward;
          return _buildImageCard(imageName, isLocked);
        },
      ),
    );
  }

  Widget _buildImageCard(String imageName, bool isLocked) {
    final String imagePath = AssetService.instance.getAssetPath(widget.category.name, imageName);

    return GestureDetector(
      onTap: () {
        if (isLocked) {
          // Immediately trigger ad for locked images
          _triggerAdToUnlock(imageName, imagePath);
          return;
        }

        // Navigate directly for unlocked images
        HapticFeedback.lightImpact();
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => CanvasColoringScreen(
              imagePath: imagePath,
              imageName: imageName,
            ),
          ),
        );
      },
      child: Container(
        decoration: BoxDecoration(
          color: const Color(0xFF2A2A2A),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: const Color(0xFF404040)),
        ),
        child: Column(
          children: [
            // Image container
            Expanded(
              child: Container(
                margin: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Stack(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: Image.asset(
                        imagePath,
                        width: double.infinity,
                        height: double.infinity,
                        fit: BoxFit.contain,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            color: Colors.white,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.image,
                                  size: 40,
                                  color: Colors.grey[400],
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  imageName,
                                  style: TextStyle(
                                    color: Colors.grey[600],
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    ),
                    // Video overlay for locked images
                    if (isLocked)
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.7),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.play_circle_fill,
                                color: Colors.red,
                                size: 32,
                              ),
                              SizedBox(height: 8),
                              Text(
                                'Watch to Unlock',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
            
            // Image name and tap to color
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                children: [
                  Text(
                    AssetService.instance.getDisplayName(imageName),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 4),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: const Color(0xFF404040),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        if (isLocked) ...[
                          const Icon(
                            Icons.videocam,
                            color: Colors.red,
                            size: 14,
                          ),
                          const SizedBox(width: 4),
                        ],
                        Text(
                          isLocked ? 'Watch to Unlock' : 'Tap to Color',
                          style: TextStyle(
                            color: isLocked ? Colors.red : Colors.white70,
                            fontSize: 12,
                            fontWeight: isLocked ? FontWeight.bold : FontWeight.normal,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Trigger ad to unlock image immediately when tapped
  Future<void> _triggerAdToUnlock(String imageName, String imagePath) async {
    HapticFeedback.lightImpact();

    final adMobService = AdMobService.instance;

    // Check if rewarded ad is available
    if (!adMobService.isRewardedReady) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Row(
            children: [
              Icon(Icons.error, color: Colors.white),
              SizedBox(width: 8),
              Text('Ad not ready. Please try again in a moment.'),
            ],
          ),
          backgroundColor: Colors.orange,
          duration: Duration(seconds: 3),
        ),
      );
      return;
    }

    try {
      // Show rewarded ad
      final success = await adMobService.showRewardedAd(
        context: 'unlock_${widget.category.name}_$imageName',
        onRewardEarned: () async {
          // Unlock the image permanently
          final rewardService = RewardService.instance;
          await rewardService.unlockPremiumImage(widget.category.name, imageName);

          // Refresh unlocked images list to update UI
          await _loadUnlockedImages();
        },
        onAdClosed: () {
          // Check if image was unlocked and navigate
          _checkAndNavigateIfUnlocked(imageName, imagePath);
        },
      );

      if (!success) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Row(
                children: [
                  Icon(Icons.error, color: Colors.white),
                  SizedBox(width: 8),
                  Text('Failed to show ad. Please try again.'),
                ],
              ),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 3),
            ),
          );
        }
      }
    } catch (e) {
      // Handle unexpected errors
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Text('Error: $e'),
              ],
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  /// Check if image was unlocked and navigate to coloring screen
  Future<void> _checkAndNavigateIfUnlocked(String imageName, String imagePath) async {
    final imageKey = '${widget.category.name}/$imageName';
    final isUnlocked = _unlockedImages.contains(imageKey);

    if (isUnlocked && mounted) {
      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Row(
            children: [
              Icon(Icons.check_circle, color: Colors.white),
              SizedBox(width: 8),
              Text('Image unlocked successfully!'),
            ],
          ),
          backgroundColor: Colors.green,
          duration: Duration(seconds: 2),
        ),
      );

      // Navigate to coloring screen
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => CanvasColoringScreen(
            imagePath: imagePath,
            imageName: imageName,
          ),
        ),
      );
    }
  }

}
