import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';

/// Utility to generate PNG coloring book images
class ImageGenerator {
  
  /// Generate a simple dragon coloring image
  static Future<Uint8List> generateDragonPNG() async {
    const int width = 400;
    const int height = 500;
    
    final ui.PictureRecorder recorder = ui.PictureRecorder();
    final Canvas canvas = Canvas(recorder);
    
    // Fill with white background
    canvas.drawRect(
      Rect.fromLTWH(0, 0, width.toDouble(), height.toDouble()),
      Paint()..color = Colors.white,
    );
    
    final Paint linePaint = Paint()
      ..color = Colors.black
      ..strokeWidth = 3
      ..style = PaintingStyle.stroke;
    
    // Dragon head (circle)
    canvas.drawCircle(
      const Offset(200, 150),
      60,
      linePaint,
    );
    
    // Dragon body (oval)
    canvas.drawOval(
      const Rect.fromLTWH(150, 200, 100, 150),
      linePaint,
    );
    
    // Left wing
    final Path leftWing = Path()
      ..moveTo(150, 220)
      ..quadraticBezierTo(80, 200, 100, 280)
      ..quadraticBezierTo(120, 320, 150, 300)
      ..close();
    canvas.drawPath(leftWing, linePaint);
    
    // Right wing
    final Path rightWing = Path()
      ..moveTo(250, 220)
      ..quadraticBezierTo(320, 200, 300, 280)
      ..quadraticBezierTo(280, 320, 250, 300)
      ..close();
    canvas.drawPath(rightWing, linePaint);
    
    // Dragon tail
    final Path tail = Path()
      ..moveTo(250, 330)
      ..quadraticBezierTo(300, 350, 280, 400)
      ..quadraticBezierTo(260, 420, 240, 380)
      ..close();
    canvas.drawPath(tail, linePaint);
    
    // Left leg
    canvas.drawOval(
      const Rect.fromLTWH(160, 340, 30, 60),
      linePaint,
    );
    
    // Right leg
    canvas.drawOval(
      const Rect.fromLTWH(210, 340, 30, 60),
      linePaint,
    );
    
    // Eyes
    canvas.drawCircle(const Offset(185, 140), 8, linePaint);
    canvas.drawCircle(const Offset(215, 140), 8, linePaint);
    
    // Eye pupils
    canvas.drawCircle(
      const Offset(185, 140),
      3,
      Paint()..color = Colors.black,
    );
    canvas.drawCircle(
      const Offset(215, 140),
      3,
      Paint()..color = Colors.black,
    );
    
    // Mouth
    final Path mouth = Path()
      ..moveTo(190, 165)
      ..quadraticBezierTo(200, 175, 210, 165);
    canvas.drawPath(mouth, linePaint);
    
    // Convert to image
    final ui.Picture picture = recorder.endRecording();
    final ui.Image image = await picture.toImage(width, height);
    final ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    
    return byteData!.buffer.asUint8List();
  }
  
  /// Generate a simple alien coloring image
  static Future<Uint8List> generateAlienPNG() async {
    const int width = 400;
    const int height = 500;
    
    final ui.PictureRecorder recorder = ui.PictureRecorder();
    final Canvas canvas = Canvas(recorder);
    
    // Fill with white background
    canvas.drawRect(
      Rect.fromLTWH(0, 0, width.toDouble(), height.toDouble()),
      Paint()..color = Colors.white,
    );
    
    final Paint linePaint = Paint()
      ..color = Colors.black
      ..strokeWidth = 3
      ..style = PaintingStyle.stroke;
    
    // Alien head (large oval)
    canvas.drawOval(
      const Rect.fromLTWH(150, 100, 100, 120),
      linePaint,
    );
    
    // Left antenna
    canvas.drawLine(
      const Offset(170, 100),
      const Offset(160, 60),
      linePaint,
    );
    canvas.drawCircle(const Offset(160, 60), 8, linePaint);
    
    // Right antenna
    canvas.drawLine(
      const Offset(230, 100),
      const Offset(240, 60),
      linePaint,
    );
    canvas.drawCircle(const Offset(240, 60), 8, linePaint);
    
    // Alien body (oval)
    canvas.drawOval(
      const Rect.fromLTWH(160, 210, 80, 120),
      linePaint,
    );
    
    // Left arm
    canvas.drawOval(
      const Rect.fromLTWH(100, 230, 70, 30),
      linePaint,
    );
    
    // Right arm
    canvas.drawOval(
      const Rect.fromLTWH(230, 230, 70, 30),
      linePaint,
    );
    
    // Left hand
    canvas.drawCircle(const Offset(110, 245), 15, linePaint);
    
    // Right hand
    canvas.drawCircle(const Offset(290, 245), 15, linePaint);
    
    // Left leg
    canvas.drawOval(
      const Rect.fromLTWH(170, 320, 25, 80),
      linePaint,
    );
    
    // Right leg
    canvas.drawOval(
      const Rect.fromLTWH(205, 320, 25, 80),
      linePaint,
    );
    
    // Large eyes
    canvas.drawCircle(const Offset(180, 150), 15, linePaint);
    canvas.drawCircle(const Offset(220, 150), 15, linePaint);
    
    // Eye pupils
    canvas.drawCircle(
      const Offset(180, 150),
      6,
      Paint()..color = Colors.black,
    );
    canvas.drawCircle(
      const Offset(220, 150),
      6,
      Paint()..color = Colors.black,
    );
    
    // Smile
    final Path smile = Path()
      ..moveTo(185, 180)
      ..quadraticBezierTo(200, 190, 215, 180);
    canvas.drawPath(smile, linePaint);
    
    // Convert to image
    final ui.Picture picture = recorder.endRecording();
    final ui.Image image = await picture.toImage(width, height);
    final ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    
    return byteData!.buffer.asUint8List();
  }
  
  /// Generate a simple flower coloring image
  static Future<Uint8List> generateFlowerPNG() async {
    const int width = 400;
    const int height = 500;
    
    final ui.PictureRecorder recorder = ui.PictureRecorder();
    final Canvas canvas = Canvas(recorder);
    
    // Fill with white background
    canvas.drawRect(
      Rect.fromLTWH(0, 0, width.toDouble(), height.toDouble()),
      Paint()..color = Colors.white,
    );
    
    final Paint linePaint = Paint()
      ..color = Colors.black
      ..strokeWidth = 3
      ..style = PaintingStyle.stroke;
    
    // Flower center
    canvas.drawCircle(const Offset(200, 200), 25, linePaint);
    
    // Petals (6 petals around center)
    for (int i = 0; i < 6; i++) {
      final double angle = (i * 60) * (3.14159 / 180); // Convert to radians
      final double petalX = 200 + 50 * cos(angle);
      final double petalY = 200 + 50 * sin(angle);
      
      canvas.drawOval(
        Rect.fromCenter(
          center: Offset(petalX, petalY),
          width: 40,
          height: 60,
        ),
        linePaint,
      );
    }
    
    // Stem
    canvas.drawLine(
      const Offset(200, 225),
      const Offset(200, 400),
      Paint()
        ..color = Colors.black
        ..strokeWidth = 8,
    );
    
    // Left leaf
    final Path leftLeaf = Path()
      ..moveTo(200, 300)
      ..quadraticBezierTo(150, 280, 160, 320)
      ..quadraticBezierTo(180, 340, 200, 320)
      ..close();
    canvas.drawPath(leftLeaf, linePaint);
    
    // Right leaf
    final Path rightLeaf = Path()
      ..moveTo(200, 350)
      ..quadraticBezierTo(250, 330, 240, 370)
      ..quadraticBezierTo(220, 390, 200, 370)
      ..close();
    canvas.drawPath(rightLeaf, linePaint);
    
    // Convert to image
    final ui.Picture picture = recorder.endRecording();
    final ui.Image image = await picture.toImage(width, height);
    final ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    
    return byteData!.buffer.asUint8List();
  }
}

// Helper function for cos calculation
double cos(double radians) {
  // Simple cosine approximation for our use case
  final angles = [1.0, 0.5, -0.5, -1.0, -0.5, 0.5]; // For 0°, 60°, 120°, 180°, 240°, 300°
  final index = ((radians * 180 / 3.14159) / 60).round() % 6;
  return angles[index];
}

// Helper function for sin calculation  
double sin(double radians) {
  // Simple sine approximation for our use case
  final angles = [0.0, 0.866, 0.866, 0.0, -0.866, -0.866]; // For 0°, 60°, 120°, 180°, 240°, 300°
  final index = ((radians * 180 / 3.14159) / 60).round() % 6;
  return angles[index];
}
