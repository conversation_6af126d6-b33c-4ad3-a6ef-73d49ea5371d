import 'dart:convert';
import 'package:flutter/services.dart';
import 'reward_service.dart';

class AssetService {
  static AssetService? _instance;
  static AssetService get instance => _instance ??= AssetService._();
  AssetService._();

  Map<String, List<String>>? _categoryImages;

  /// Get all images for a specific category
  Future<List<String>> getImagesForCategory(String categoryPath) async {
    await _loadAllImages();
    return _categoryImages?[categoryPath] ?? [];
  }

  /// Get all available categories
  Future<List<String>> getAllCategories() async {
    await _loadAllImages();
    return _categoryImages?.keys.toList() ?? [];
  }

  /// Load all images from asset manifest
  Future<void> _loadAllImages() async {
    if (_categoryImages != null) return; // Already loaded

    try {
      // Get the asset manifest
      final manifestContent = await rootBundle.loadString('AssetManifest.json');
      final Map<String, dynamic> manifestMap = json.decode(manifestContent);

      // Initialize category map
      _categoryImages = <String, List<String>>{};

      // Filter for PNG files in images directories
      final imagePaths = manifestMap.keys
          .where((String key) => key.startsWith('assets/images/'))
          .where((String key) => key.toLowerCase().endsWith('.png'))
          .where((String key) => !key.contains('README'))
          .toList();

      // Group images by category
      for (String imagePath in imagePaths) {
        final pathParts = imagePath.split('/');
        if (pathParts.length >= 3) {
          final categoryName = pathParts[2]; // e.g., 'animals', 'food', etc.
          final fileName = pathParts.last.split('.').first; // Remove .png extension
          
          if (!_categoryImages!.containsKey(categoryName)) {
            _categoryImages![categoryName] = [];
          }
          _categoryImages![categoryName]!.add(fileName);
        }
      }

      // Sort images in each category
      _categoryImages!.forEach((category, images) {
        images.sort();
      });

      print('📁 Loaded categories: ${_categoryImages!.keys.toList()}');
      _categoryImages!.forEach((category, images) {
        print('📂 $category: ${images.length} images');
      });

    } catch (e) {
      print('❌ Error loading asset manifest: $e');
      _categoryImages = {};
    }
  }

  /// Get formatted display name for image
  String getDisplayName(String fileName) {
    return fileName
        .split('_')
        .map((word) => word.isNotEmpty 
            ? word[0].toUpperCase() + word.substring(1).toLowerCase()
            : '')
        .join(' ');
  }

  /// Get full asset path for image
  String getAssetPath(String categoryName, String fileName) {
    // For premium category, claimed daily images are still in the premium folder
    if (categoryName == 'premium') {
      return 'assets/images/premium/$fileName.png';
    }
    return 'assets/images/$categoryName/$fileName.png';
  }

  /// Check if image exists in assets
  Future<bool> imageExists(String assetPath) async {
    try {
      await rootBundle.load(assetPath);
      return true;
    } catch (e) {
      return false;
    }
  }
}

/// Category information model
class CategoryData {
  final String name;
  final String displayName;
  final String icon;
  final int color;
  final List<String> images;
  final List<String> availableImages; // Images user can access (free + unlocked)
  final List<String> lockedImages; // Premium images that are locked

  CategoryData({
    required this.name,
    required this.displayName,
    required this.icon,
    required this.color,
    required this.images,
    this.availableImages = const [],
    this.lockedImages = const [],
  });

  static List<CategoryData> getDefaultCategories() {
    return [
      CategoryData(
        name: 'abstract_mandalas',
        displayName: 'Abstract Mandalas',
        icon: '🌸',
        color: 0xFFFF6B9D,
        images: [],
      ),
      CategoryData(
        name: 'animals',
        displayName: 'Animals',
        icon: '🦒',
        color: 0xFFFF8A5B,
        images: [],
      ),
      CategoryData(
        name: 'fantasy_characters',
        displayName: 'Fantasy Characters',
        icon: '🧚',
        color: 0xFFFF6B9D,
        images: [],
      ),
      CategoryData(
        name: 'food',
        displayName: 'Food',
        icon: '🎨',
        color: 0xFFFF8A5B,
        images: [],
      ),
      CategoryData(
        name: 'nature',
        displayName: 'Nature',
        icon: '🌿',
        color: 0xFFFF6B9D,
        images: [],
      ),
      CategoryData(
        name: 'objects',
        displayName: 'Objects',
        icon: '⚡',
        color: 0xFFFF8A5B,
        images: [],
      ),
      CategoryData(
        name: 'premium',
        displayName: 'Premium',
        icon: '💎',
        color: 0xFFFFD700,
        images: [],
      ),
    ];
  }

  /// Create CategoryData with loaded images and reward system
  static Future<CategoryData> withImages(CategoryData base) async {
    final images = await AssetService.instance.getImagesForCategory(base.name);

    // Special handling for premium category - only show claimed daily bonus images
    if (base.name == 'premium') {
      final rewardService = RewardService.instance;
      final claimedDailyImages = await rewardService.getClaimedDailyBonuses();

      return CategoryData(
        name: base.name,
        displayName: base.displayName,
        icon: base.icon,
        color: base.color,
        images: claimedDailyImages, // Only show claimed daily bonus images
        availableImages: claimedDailyImages, // All claimed daily images are available
        lockedImages: [], // No locked images in premium category
      );
    }

    // Use reward service
    final rewardService = RewardService.instance;

    final availableImages = await rewardService.getAvailableImages(base.name, images);
    final premiumImages = rewardService.getPremiumImages(images);
    final unlockedImages = await rewardService.getUnlockedImages();

    // Calculate locked images (premium images that aren't unlocked)
    final lockedImages = premiumImages.where((image) =>
      !unlockedImages.contains('${base.name}/$image')).toList();

    return CategoryData(
      name: base.name,
      displayName: base.displayName,
      icon: base.icon,
      color: base.color,
      images: images,
      availableImages: availableImages,
      lockedImages: lockedImages,
    );
  }
}
