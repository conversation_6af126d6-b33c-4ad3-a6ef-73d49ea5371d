import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'saved_images_service.dart';

// Conditional imports
import 'image_save_web.dart' if (dart.library.io) 'image_save_mobile.dart';

class ImageSaveService {
  static ImageSaveService? _instance;
  static ImageSaveService get instance => _instance ??= ImageSaveService._();
  ImageSaveService._();

  /// Save image with platform-specific implementation
  Future<bool> saveImage({
    required String imageName,
    required String originalImagePath,
    required Uint8List imageData,
    required Function(String) showMessage,
  }) async {
    return await saveImagePlatform(
      imageName: imageName,
      originalImagePath: originalImagePath,
      imageData: imageData,
      showMessage: showMessage,
    );
  }
}
