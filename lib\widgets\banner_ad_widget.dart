import 'package:flutter/material.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import '../services/admob_service.dart';

/// Reusable banner ad widget with smart loading and error handling
class BannerAdWidget extends StatefulWidget {
  final EdgeInsets? margin;
  final Color? backgroundColor;
  final bool showLoadingIndicator;
  final String? context; // For analytics/debugging

  const BannerAdWidget({
    super.key,
    this.margin,
    this.backgroundColor,
    this.showLoadingIndicator = true,
    this.context,
  });

  @override
  State<BannerAdWidget> createState() => _BannerAdWidgetState();
}

class _BannerAdWidgetState extends State<BannerAdWidget> {
  final AdMobService _adMobService = AdMobService.instance;
  bool _isVisible = true;
  BannerAd? _bannerAd;
  bool _isAdLoaded = false;

  @override
  void initState() {
    super.initState();
    // Ensure AdMob is initialized and create unique banner ad
    _initializeAd();

    // Debug logging
    if (widget.context != null) {
      print('🎯 BannerAdWidget initialized for: ${widget.context}');
    }
  }

  Future<void> _initializeAd() async {
    await _adMobService.initialize();
    _createAndLoadAd();
  }

  void _createAndLoadAd() {
    _bannerAd = _adMobService.createBannerAd(context: widget.context);
    if (_bannerAd != null) {
      _bannerAd!.load().then((_) {
        if (mounted) {
          setState(() {
            _isAdLoaded = true;
          });
        }
      }).catchError((error) {
        if (mounted) {
          setState(() {
            _isAdLoaded = false;
          });
        }
      });
    }
  }

  @override
  void dispose() {
    _bannerAd?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!_isVisible) return const SizedBox.shrink();

    return Container(
      margin: widget.margin ?? const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        color: widget.backgroundColor ?? Colors.transparent,
        borderRadius: BorderRadius.circular(8),
      ),
      child: _buildAdContent(),
    );
  }

  Widget _buildAdContent() {
    print('🎯 Banner widget build - Ad: ${_bannerAd != null}, Ready: $_isAdLoaded, Context: ${widget.context}');

    if (_bannerAd != null && _isAdLoaded) {
      print('✅ Displaying banner ad for: ${widget.context}');
      return _buildLoadedAd(_bannerAd!);
    } else {
      // Return empty space when ads fail to load - no placeholder
      print('🎯 No ad to show for: ${widget.context}');
      return _buildPlaceholderAd();
    }
  }

  Widget _buildLoadedAd(BannerAd bannerAd) {
    return Container(
      alignment: Alignment.center,
      width: bannerAd.size.width.toDouble(),
      height: bannerAd.size.height.toDouble(),
      child: AdWidget(ad: bannerAd),
    );
  }

  Widget _buildLoadingState() {
    return Container(
      height: 50, // Standard banner height
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: Colors.grey.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.grey.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                Colors.grey.withOpacity(0.6),
              ),
            ),
          ),
          const SizedBox(width: 8),
          Text(
            'Loading ad...',
            style: TextStyle(
              color: Colors.grey.withOpacity(0.8),
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlaceholderAd() {
    // Return empty space when ads fail to load - no placeholder
    return const SizedBox.shrink();
  }

  /// Hide the banner ad (useful for premium users)
  void hide() {
    setState(() {
      _isVisible = false;
    });
  }

  /// Show the banner ad
  void show() {
    setState(() {
      _isVisible = true;
    });
  }
}

/// Smart banner ad widget that automatically manages placement
class SmartBannerAd extends StatelessWidget {
  final String context;
  final bool showAtBottom;
  final EdgeInsets? padding;

  const SmartBannerAd({
    super.key,
    required this.context,
    this.showAtBottom = true,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Container(
        padding: padding ?? const EdgeInsets.all(8),
        child: BannerAdWidget(
          context: this.context,
          backgroundColor: Colors.black.withOpacity(0.05),
          margin: const EdgeInsets.symmetric(horizontal: 8),
        ),
      ),
    );
  }
}

/// Banner ad for specific screen sections
class SectionBannerAd extends StatelessWidget {
  final String section;
  final bool compact;

  const SectionBannerAd({
    super.key,
    required this.section,
    this.compact = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(
        vertical: compact ? 4 : 8,
        horizontal: compact ? 4 : 8,
      ),
      child: BannerAdWidget(
        context: section,
        backgroundColor: compact ? Colors.transparent : Colors.black.withOpacity(0.02),
        showLoadingIndicator: !compact,
      ),
    );
  }
}
