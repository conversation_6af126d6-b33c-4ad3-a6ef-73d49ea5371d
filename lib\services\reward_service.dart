import 'package:shared_preferences/shared_preferences.dart';

class RewardService {
  static RewardService? _instance;
  static RewardService get instance => _instance ??= RewardService._();
  RewardService._();

  static const String _dailyBonusKey = 'daily_bonus_claimed';
  static const String _unlockedImagesKey = 'unlocked_premium_images'; // For category locked images
  static const String _lastClaimTimestampKey = 'last_daily_bonus_claim_timestamp';
  static const String _claimedDailyBonusesKey = 'claimed_daily_bonuses'; // For premium folder
  static const String _nextDayNumberKey = 'next_daily_bonus_day_number'; // Sequential day progression

  /// Check if daily bonus is available today
  Future<bool> isDailyBonusAvailable() async {
    final prefs = await SharedPreferences.getInstance();
    final lastClaimTimestamp = prefs.getInt(_lastClaimTimestampKey);

    if (lastClaimTimestamp == null) return true;

    final lastClaim = DateTime.fromMillisecondsSinceEpoch(lastClaimTimestamp);
    final now = DateTime.now();
    final timeDifference = now.difference(lastClaim);

    return timeDifference.inHours >= 24; // 24 hours for production
  }

  /// Get today's daily bonus image (sequential progression)
  Future<String> getTodaysBonusImage() async {
    final prefs = await SharedPreferences.getInstance();
    final dayNumber = prefs.getInt(_nextDayNumberKey) ?? 1;
    return 'day_$dayNumber';
  }

  /// Get current day number synchronously (for immediate access)
  String getTodaysBonusImageSync() {
    // This is a fallback for synchronous access
    // The actual day number should be retrieved asynchronously
    return 'day_1'; // Default fallback
  }

  /// Claim today's daily bonus
  Future<bool> claimDailyBonus() async {
    if (!await isDailyBonusAvailable()) {
      return false; // Already claimed today
    }

    final prefs = await SharedPreferences.getInstance();
    final bonusImage = await getTodaysBonusImage();
    final now = DateTime.now();

    // Store timestamp for cooldown tracking
    await prefs.setInt(_lastClaimTimestampKey, now.millisecondsSinceEpoch);

    // Increment the day number for sequential progression
    final currentDayNumber = prefs.getInt(_nextDayNumberKey) ?? 1;
    final nextDayNumber = currentDayNumber >= 30 ? 1 : currentDayNumber + 1;
    await prefs.setInt(_nextDayNumberKey, nextDayNumber);

    // Add the daily bonus image to premium category
    await addDailyBonusToPremiumCategory(bonusImage);

    // Daily bonus claimed successfully
    return true;
  }

  /// Unlock a premium image
  Future<void> unlockPremiumImage(String category, String imageName) async {
    final prefs = await SharedPreferences.getInstance();
    final unlockedImages = await getUnlockedImages();
    
    final imageKey = '$category/$imageName';
    if (!unlockedImages.contains(imageKey)) {
      unlockedImages.add(imageKey);
      await prefs.setStringList(_unlockedImagesKey, unlockedImages);
      print('🔓 Unlocked premium image: $imageKey');
    }
  }

  /// Get list of unlocked premium images
  Future<List<String>> getUnlockedImages() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getStringList(_unlockedImagesKey) ?? [];
  }

  /// Check if a specific image is unlocked
  Future<bool> isImageUnlocked(String category, String imageName) async {
    final unlockedImages = await getUnlockedImages();
    return unlockedImages.contains('$category/$imageName');
  }

  /// Get premium images for a category (half of the images)
  List<String> getPremiumImages(List<String> allImages) {
    if (allImages.length <= 2) return []; // Don't make premium if too few images
    
    final halfCount = (allImages.length / 2).ceil();
    // Take the second half as premium
    return allImages.skip(allImages.length - halfCount).toList();
  }

  /// Get free images for a category (first half)
  List<String> getFreeImages(List<String> allImages) {
    if (allImages.length <= 2) return allImages; // All free if too few images
    
    final halfCount = (allImages.length / 2).ceil();
    // Take the first half as free
    return allImages.take(allImages.length - halfCount).toList();
  }

  /// Filter images based on unlock status
  Future<List<String>> getAvailableImages(String category, List<String> allImages) async {
    final freeImages = getFreeImages(allImages);
    final premiumImages = getPremiumImages(allImages);
    final unlockedImages = await getUnlockedImages();
    
    final availableImages = <String>[];
    
    // Add all free images
    availableImages.addAll(freeImages);
    
    // Add unlocked premium images
    for (final premiumImage in premiumImages) {
      if (unlockedImages.contains('$category/$premiumImage')) {
        availableImages.add(premiumImage);
      }
    }
    
    return availableImages;
  }

  /// Check if an image is premium (locked)
  bool isImagePremium(List<String> allImages, String imageName) {
    final premiumImages = getPremiumImages(allImages);
    return premiumImages.contains(imageName);
  }

  /// Get time until next bonus (for UI display)
  Future<Duration?> getTimeUntilNextBonus() async {
    final prefs = await SharedPreferences.getInstance();
    final lastClaimTimestamp = prefs.getInt(_lastClaimTimestampKey);

    if (lastClaimTimestamp == null) return null;

    final lastClaim = DateTime.fromMillisecondsSinceEpoch(lastClaimTimestamp);
    final now = DateTime.now();
    final timeDifference = now.difference(lastClaim);

    const cooldownDuration = Duration(hours: 24); // 60 seconds for testing
    final remainingTime = cooldownDuration - timeDifference;

    return remainingTime.isNegative ? null : remainingTime;
  }



  /// Reset all rewards (for testing/debugging)
  Future<void> resetAllRewards() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_dailyBonusKey);
    await prefs.remove(_unlockedImagesKey);
    await prefs.remove(_lastClaimTimestampKey);
    await prefs.remove(_claimedDailyBonusesKey);
    await prefs.remove(_nextDayNumberKey);
    // All rewards reset
  }

  /// Get total unlocked premium images count
  Future<int> getUnlockedCount() async {
    final unlockedImages = await getUnlockedImages();
    return unlockedImages.length;
  }

  /// Unlock multiple images (for special rewards)
  Future<void> unlockMultipleImages(List<String> imageKeys) async {
    for (final imageKey in imageKeys) {
      final parts = imageKey.split('/');
      if (parts.length == 2) {
        await unlockPremiumImage(parts[0], parts[1]);
      }
    }
  }

  /// Add a daily bonus image to the premium category
  Future<void> addDailyBonusToPremiumCategory(String imageName) async {
    final prefs = await SharedPreferences.getInstance();
    final claimedDailyBonuses = await getClaimedDailyBonuses();

    if (!claimedDailyBonuses.contains(imageName)) {
      claimedDailyBonuses.add(imageName);
      await prefs.setStringList(_claimedDailyBonusesKey, claimedDailyBonuses);
      print('📁 Added daily bonus $imageName to premium category');
    }
  }

  /// Get list of claimed daily bonus images in premium category
  Future<List<String>> getClaimedDailyBonuses() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getStringList(_claimedDailyBonusesKey) ?? [];
  }

  /// Check if a daily bonus image is claimed (in premium category)
  Future<bool> isDailyBonusClaimed(String imageName) async {
    final claimedDailyBonuses = await getClaimedDailyBonuses();
    return claimedDailyBonuses.contains(imageName);
  }

  /// Reset premium category (remove all claimed daily bonuses)
  Future<void> resetPremiumCategory() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_claimedDailyBonusesKey);
    print('🗑️ Premium category reset - all daily bonuses removed');
  }

  /// Debug method to test the system (development only)
  Future<void> debugPrintStatus() async {
    // Debug information available in development mode only
  }

  /// Force reset daily bonus for testing (development only)
  Future<void> resetDailyBonusForTesting() async {
    // Testing functionality available in development mode only
  }
}
