import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../services/svg_coloring_service.dart';

class SvgColoringScreen extends StatefulWidget {
  final String imagePath;
  final String imageName;

  const SvgColoringScreen({
    super.key,
    required this.imagePath,
    required this.imageName,
  });

  @override
  State<SvgColoringScreen> createState() => _SvgColoringScreenState();
}

class _SvgColoringScreenState extends State<SvgColoringScreen> {
  Color _selectedColor = Colors.red;
  final SvgColoringService _coloringService = SvgColoringService();
  String? _currentSvgContent;
  bool _isLoading = true;
  String? _error;

  // Available colors for coloring
  final List<Color> _colors = [
    Colors.red,
    Colors.blue,
    Colors.green,
    Colors.yellow,
    Colors.orange,
    Colors.purple,
    Colors.pink,
    Colors.brown,
  ];

  @override
  void initState() {
    super.initState();
    _loadSvgContent();
  }

  /// Load and initialize SVG content
  Future<void> _loadSvgContent() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      String svgContent;

      if (widget.imagePath.isEmpty) {
        // Handle custom SVG content (from upload)
        svgContent = '''<?xml version="1.0" encoding="UTF-8"?>
<svg width="300" height="400" viewBox="0 0 300 400" xmlns="http://www.w3.org/2000/svg">
  <text x="150" y="200" text-anchor="middle" fill="black">Custom SVG</text>
</svg>''';
      } else {
        // Load SVG from assets
        svgContent = await rootBundle.loadString(widget.imagePath);
      }

      // Initialize the coloring service
      await _coloringService.initialize(svgContent);
      _currentSvgContent = svgContent;

      setState(() {
        _isLoading = false;
      });

      print('✅ SVG loaded successfully: ${widget.imageName}');

    } catch (e) {
      setState(() {
        _isLoading = false;
        _error = 'Failed to load SVG: $e';
      });
      print('❌ Error loading SVG: $e');
    }
  }

  /// Handle tap on SVG to color areas
  void _handleSvgTap(TapDownDetails details) {
    if (_currentSvgContent == null) return;

    try {
      // Get the render box to calculate local coordinates
      final RenderBox? renderBox = context.findRenderObject() as RenderBox?;
      if (renderBox == null) return;

      // Calculate the SVG widget area (accounting for app bar and bottom controls)
      final screenSize = MediaQuery.of(context).size;
      final appBarHeight = kToolbarHeight + MediaQuery.of(context).padding.top;
      final bottomControlsHeight = 200; // Approximate height of color controls

      final svgWidgetHeight = screenSize.height - appBarHeight - bottomControlsHeight;
      final svgWidgetSize = Size(screenSize.width, svgWidgetHeight);

      // Convert global position to local position within SVG widget
      final globalPosition = details.globalPosition;
      final localPosition = Offset(
        globalPosition.dx,
        globalPosition.dy - appBarHeight, // Subtract app bar height
      );

      print('🎯 Tap at global: $globalPosition, local: $localPosition');

      // Detect which path was tapped
      final tappedPathId = _coloringService.detectTappedPath(localPosition, svgWidgetSize);

      if (tappedPathId != null) {
        // Color the tapped path
        final updatedSvg = _coloringService.colorPath(tappedPathId, _selectedColor);

        setState(() {
          _currentSvgContent = updatedSvg;
        });

        print('🎨 Colored path: $tappedPathId');

        // Provide haptic feedback
        HapticFeedback.lightImpact();
      } else {
        print('❌ No colorable area found at tap location');
      }

    } catch (e) {
      print('❌ Error handling SVG tap: $e');
    }
  }

  /// Clear all colors
  void _clearAllColors() {
    if (_currentSvgContent == null) return;

    try {
      final clearedSvg = _coloringService.clearAllColors();

      setState(() {
        _currentSvgContent = clearedSvg;
      });

      print('🧹 All colors cleared');
      HapticFeedback.mediumImpact();

    } catch (e) {
      print('❌ Error clearing colors: $e');
    }
  }

  /// Build SVG content with tap handling
  Widget _buildSvgContent() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading SVG...'),
          ],
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, color: Colors.red, size: 48),
            const SizedBox(height: 16),
            Text(_error!, textAlign: TextAlign.center),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadSvgContent,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_currentSvgContent == null) {
      return const Center(child: Text('No SVG content available'));
    }

    return GestureDetector(
      onTapDown: _handleSvgTap,
      child: SvgPicture.string(
        _currentSvgContent!,
        fit: BoxFit.contain,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: AppBar(
        title: Text(
          'Color: ${widget.imageName}',
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Colors.pink,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: _clearAllColors,
          ),
        ],
      ),
      body: Column(
        children: [
          // Main coloring area
          Expanded(
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              child: Center(
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: InteractiveViewer(
                      panEnabled: true,
                      scaleEnabled: true,
                      minScale: 0.5,
                      maxScale: 3.0,
                      child: Container(
                        padding: const EdgeInsets.all(20),
                        child: _buildSvgContent(),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),

          // Instructions
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
            child: const Text(
              '🎨 Tap any area to color it! Select a color below and tap on the drawing.',
              style: TextStyle(
                fontSize: 16,
                color: Colors.black87,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ),

          // Color palette
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: Column(
              children: [
                // Color selection
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: _colors.map((color) {
                    final isSelected = _selectedColor == color;
                    return GestureDetector(
                      onTap: () {
                        setState(() {
                          _selectedColor = color;
                        });
                      },
                      child: Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: color,
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: isSelected ? Colors.black : Colors.grey[300]!,
                            width: isSelected ? 3 : 1,
                          ),
                          boxShadow: isSelected
                              ? [
                                  BoxShadow(
                                    color: color.withValues(alpha: 0.4),
                                    blurRadius: 8,
                                    spreadRadius: 2,
                                  ),
                                ]
                              : null,
                        ),
                      ),
                    );
                  }).toList(),
                ),

                const SizedBox(height: 16),

                // Clear button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: _clearAllColors,
                    icon: const Icon(Icons.clear, color: Colors.white),
                    label: const Text(
                      'Clear All Colors',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
