import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/ad_test_service.dart';
import '../services/reward_service.dart';

/// Screen for testing different ad scenarios
class AdTestScreen extends StatefulWidget {
  const AdTestScreen({super.key});

  @override
  State<AdTestScreen> createState() => _AdTestScreenState();
}

class _AdTestScreenState extends State<AdTestScreen> {
  final AdTestService _adTestService = AdTestService.instance;
  final RewardService _rewardService = RewardService.instance;
  
  AdTestScenario _selectedScenario = AdTestScenario.success;
  bool _isTestRunning = false;
  String _testStatus = 'Ready to test';
  String? _testResult;
  List<String> _unlockedImages = [];

  @override
  void initState() {
    super.initState();
    _loadUnlockedImages();
    _adTestService.setTestMode(true);
  }

  Future<void> _loadUnlockedImages() async {
    final unlocked = await _rewardService.getUnlockedImages();
    setState(() {
      _unlockedImages = unlocked;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1E1E1E),
      appBar: AppBar(
        backgroundColor: const Color(0xFF2C2C2C),
        title: const Text(
          'Ad Test Center',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildTestModeCard(),
            const SizedBox(height: 16),
            _buildScenarioSelector(),
            const SizedBox(height: 16),
            _buildTestControls(),
            const SizedBox(height: 16),
            _buildTestStatus(),
            const SizedBox(height: 16),
            _buildUnlockedImagesList(),
          ],
        ),
      ),
    );
  }

  Widget _buildTestModeCard() {
    return Card(
      color: const Color(0xFF2C2C2C),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.science,
                  color: Colors.green,
                  size: 24,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Test Mode Active',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            const Text(
              'This screen allows you to test different ad scenarios without real ads. Perfect for testing unlock functionality and error handling.',
              style: TextStyle(color: Colors.white70, fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildScenarioSelector() {
    return Card(
      color: const Color(0xFF2C2C2C),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Select Test Scenario',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            ...AdTestScenario.values.map((scenario) {
              final isSelected = scenario == _selectedScenario;
              return Container(
                margin: const EdgeInsets.only(bottom: 8),
                decoration: BoxDecoration(
                  color: isSelected ? Colors.blue.withOpacity(0.2) : Colors.transparent,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: isSelected ? Colors.blue : Colors.grey.withOpacity(0.3),
                  ),
                ),
                child: ListTile(
                  title: Text(
                    _getScenarioName(scenario),
                    style: TextStyle(
                      color: isSelected ? Colors.blue : Colors.white,
                      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                  subtitle: Text(
                    _getScenarioDescription(scenario),
                    style: TextStyle(
                      color: isSelected ? Colors.blue.withOpacity(0.8) : Colors.white70,
                      fontSize: 12,
                    ),
                  ),
                  leading: Radio<AdTestScenario>(
                    value: scenario,
                    groupValue: _selectedScenario,
                    onChanged: _isTestRunning ? null : (value) {
                      setState(() {
                        _selectedScenario = value!;
                      });
                      _adTestService.setTestScenario(value!);
                    },
                    activeColor: Colors.blue,
                  ),
                  onTap: _isTestRunning ? null : () {
                    setState(() {
                      _selectedScenario = scenario;
                    });
                    _adTestService.setTestScenario(scenario);
                  },
                ),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildTestControls() {
    return Card(
      color: const Color(0xFF2C2C2C),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Test Controls',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isTestRunning ? null : _runAdTest,
                    icon: Icon(_isTestRunning ? Icons.hourglass_empty : Icons.play_arrow),
                    label: Text(_isTestRunning ? 'Testing...' : 'Run Ad Test'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _clearTestData,
                    icon: const Icon(Icons.clear_all),
                    label: const Text('Clear Data'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTestStatus() {
    return Card(
      color: const Color(0xFF2C2C2C),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Test Status',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.3),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Status: $_testStatus',
                    style: const TextStyle(color: Colors.white, fontSize: 14),
                  ),
                  if (_testResult != null) ...[
                    const SizedBox(height: 8),
                    Text(
                      'Result: $_testResult',
                      style: TextStyle(
                        color: _testResult!.contains('SUCCESS') ? Colors.green : Colors.red,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUnlockedImagesList() {
    return Card(
      color: const Color(0xFF2C2C2C),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Unlocked Images (${_unlockedImages.length})',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            if (_unlockedImages.isEmpty)
              const Text(
                'No images unlocked yet. Run a successful ad test to unlock test images.',
                style: TextStyle(color: Colors.white70, fontSize: 14),
              )
            else
              ...(_unlockedImages.map((imageKey) {
                return Container(
                  margin: const EdgeInsets.only(bottom: 4),
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(color: Colors.green.withOpacity(0.3)),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.lock_open, color: Colors.green, size: 16),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          imageKey,
                          style: const TextStyle(color: Colors.white, fontSize: 14),
                        ),
                      ),
                    ],
                  ),
                );
              }).toList()),
          ],
        ),
      ),
    );
  }

  Future<void> _runAdTest() async {
    setState(() {
      _isTestRunning = true;
      _testStatus = 'Initializing test...';
      _testResult = null;
    });

    HapticFeedback.lightImpact();

    try {
      final result = await _adTestService.simulateRewardedAd(
        imageName: 'test_image.png',
        category: 'TestCategory',
        onStatusUpdate: (status) {
          setState(() {
            _testStatus = status;
          });
        },
      );

      if (result.success && result.rewardEarned) {
        // Simulate unlocking a test image
        await _rewardService.unlockPremiumImage('TestCategory', 'test_image.png');
        await _loadUnlockedImages();
        
        setState(() {
          _testResult = 'SUCCESS - Image unlocked!';
          _testStatus = 'Test completed successfully';
        });
      } else {
        setState(() {
          _testResult = 'FAILED - ${result.error ?? 'Unknown error'}';
          _testStatus = 'Test failed';
        });
      }
    } catch (e) {
      setState(() {
        _testResult = 'ERROR - $e';
        _testStatus = 'Test error occurred';
      });
    } finally {
      setState(() {
        _isTestRunning = false;
      });
    }
  }

  Future<void> _clearTestData() async {
    HapticFeedback.lightImpact();

    // Clear all unlocked images (for testing purposes)
    final prefs = await _rewardService.getUnlockedImages();
    // Note: In a real app, you might want to only clear test images
    // For now, we'll just reload to show current state
    await _loadUnlockedImages();

    setState(() {
      _testStatus = 'Ready to test';
      _testResult = null;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Test data cleared'),
        backgroundColor: Colors.green,
      ),
    );
  }

  String _getScenarioName(AdTestScenario scenario) {
    switch (scenario) {
      case AdTestScenario.success:
        return 'Success (Normal)';
      case AdTestScenario.loadFailure:
        return 'Load Failure';
      case AdTestScenario.watchFailure:
        return 'Watch Failure';
      case AdTestScenario.slowLoading:
        return 'Slow Loading';
      case AdTestScenario.quickSuccess:
        return 'Quick Success';
      case AdTestScenario.userCanceled:
        return 'User Canceled';
    }
  }

  String _getScenarioDescription(AdTestScenario scenario) {
    switch (scenario) {
      case AdTestScenario.success:
        return 'Ad loads normally and completes successfully';
      case AdTestScenario.loadFailure:
        return 'Ad fails to load due to network/server issues';
      case AdTestScenario.watchFailure:
        return 'Ad loads but fails during playback';
      case AdTestScenario.slowLoading:
        return 'Ad takes longer than usual to load';
      case AdTestScenario.quickSuccess:
        return 'Ad loads and completes faster than normal';
      case AdTestScenario.userCanceled:
        return 'User cancels ad before completion';
    }
  }
}
