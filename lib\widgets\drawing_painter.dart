import 'package:flutter/material.dart';
import 'dart:ui' as ui;
import 'dart:typed_data';

enum DrawingTool { brush, fill, eraser }

class AdvancedDrawingPainter extends CustomPainter {
  final List<List<Offset?>> strokes;
  final List<Color> strokeColors;
  final List<double> strokeWidths;
  final List<DrawingTool> strokeTools;
  final List<Offset?> currentStroke;
  final Color currentColor;
  final double currentStrokeWidth;
  final DrawingTool currentTool;

  AdvancedDrawingPainter({
    required this.strokes,
    required this.strokeColors,
    required this.strokeWidths,
    required this.strokeTools,
    required this.currentStroke,
    required this.currentColor,
    required this.currentStrokeWidth,
    required this.currentTool,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Draw all completed strokes
    for (int i = 0; i < strokes.length; i++) {
      if (i < strokeColors.length && i < strokeWidths.length && i < strokeTools.length) {
        _drawStroke(
          canvas,
          strokes[i],
          strokeColors[i],
          strokeWidths[i],
          strokeTools[i],
        );
      }
    }

    // Draw current stroke
    if (currentStroke.isNotEmpty) {
      _drawStroke(
        canvas,
        currentStroke,
        currentColor,
        currentStrokeWidth,
        currentTool,
      );
    }
  }

  void _drawStroke(
    Canvas canvas,
    List<Offset?> stroke,
    Color color,
    double strokeWidth,
    DrawingTool tool,
  ) {
    if (stroke.isEmpty) return;

    Paint paint = Paint()
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round
      ..strokeWidth = strokeWidth;

    switch (tool) {
      case DrawingTool.brush:
        // For coloring book: use a softer, more natural brush
        paint.color = color.withValues(alpha: 0.8); // Slightly transparent for natural look
        paint.style = PaintingStyle.stroke;
        break;
      case DrawingTool.eraser:
        // Eraser removes user's coloring, not the original lines
        paint.color = Colors.white;
        paint.style = PaintingStyle.stroke;
        paint.strokeWidth = strokeWidth * 1.5; // Eraser is bigger
        break;
      case DrawingTool.fill:
        // Fill tool creates filled areas, not strokes
        paint.color = color;
        paint.style = PaintingStyle.fill;
        break;
    }

    if (tool == DrawingTool.fill) {
      // For fill tool, create filled circles at each point
      for (Offset? point in stroke) {
        if (point != null) {
          canvas.drawCircle(point, strokeWidth * 3, paint);
        }
      }
    } else {
      // Draw the stroke path for brush and eraser
      Path path = Path();
      bool pathStarted = false;

      for (int i = 0; i < stroke.length; i++) {
        if (stroke[i] == null) {
          // End of a continuous line
          pathStarted = false;
        } else {
          if (!pathStarted) {
            path.moveTo(stroke[i]!.dx, stroke[i]!.dy);
            pathStarted = true;
          } else {
            path.lineTo(stroke[i]!.dx, stroke[i]!.dy);
          }
        }
      }

      canvas.drawPath(path, paint);
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => true;
}

// Flood fill algorithm for the fill tool
class FloodFill {
  static Future<ui.Image?> floodFill(
    ui.Image image,
    Offset point,
    Color fillColor,
  ) async {
    try {
      // Convert image to byte data
      final ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.rawRgba);
      if (byteData == null) return null;

      final Uint8List pixels = byteData.buffer.asUint8List();
      final int width = image.width;
      final int height = image.height;

      // Get the target pixel position
      final int x = point.dx.round();
      final int y = point.dy.round();

      // Check bounds
      if (x < 0 || x >= width || y < 0 || y >= height) return null;

      // Get the target color at the clicked point
      final int targetIndex = (y * width + x) * 4;
      final Color targetColor = Color.fromARGB(
        pixels[targetIndex + 3], // Alpha
        pixels[targetIndex],     // Red
        pixels[targetIndex + 1], // Green
        pixels[targetIndex + 2], // Blue
      );

      // If the target color is the same as fill color, no need to fill
      if (targetColor == fillColor) return null;

      // Perform flood fill
      _floodFillRecursive(pixels, width, height, x, y, targetColor, fillColor);

      // Create new image from modified pixels
      final ui.Codec codec = await ui.instantiateImageCodec(
        pixels.buffer.asUint8List(),
        targetWidth: width,
        targetHeight: height,
      );
      final ui.FrameInfo frameInfo = await codec.getNextFrame();
      return frameInfo.image;
    } catch (e) {
      print('Flood fill error: $e');
      return null;
    }
  }

  static void _floodFillRecursive(
    Uint8List pixels,
    int width,
    int height,
    int x,
    int y,
    Color targetColor,
    Color fillColor,
  ) {
    // Check bounds
    if (x < 0 || x >= width || y < 0 || y >= height) return;

    final int index = (y * width + x) * 4;
    
    // Get current pixel color
    final Color currentColor = Color.fromARGB(
      pixels[index + 3], // Alpha
      pixels[index],     // Red
      pixels[index + 1], // Green
      pixels[index + 2], // Blue
    );

    // If current color is not the target color, stop
    if (currentColor != targetColor) return;

    // Set the new color
    pixels[index] = fillColor.red;
    pixels[index + 1] = fillColor.green;
    pixels[index + 2] = fillColor.blue;
    pixels[index + 3] = fillColor.alpha;

    // Recursively fill adjacent pixels
    _floodFillRecursive(pixels, width, height, x + 1, y, targetColor, fillColor);
    _floodFillRecursive(pixels, width, height, x - 1, y, targetColor, fillColor);
    _floodFillRecursive(pixels, width, height, x, y + 1, targetColor, fillColor);
    _floodFillRecursive(pixels, width, height, x, y - 1, targetColor, fillColor);
  }
}

// Simple drawing painter for backward compatibility
class DrawingPainter extends CustomPainter {
  final List<Offset?> points;
  final Color color;
  final double strokeWidth;

  DrawingPainter({
    required this.points,
    required this.color,
    required this.strokeWidth,
  });

  @override
  void paint(Canvas canvas, Size size) {
    Paint paint = Paint()
      ..color = color
      ..strokeCap = StrokeCap.round
      ..strokeWidth = strokeWidth;

    for (int i = 0; i < points.length - 1; i++) {
      if (points[i] != null && points[i + 1] != null) {
        canvas.drawLine(points[i]!, points[i + 1]!, paint);
      }
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => true;
}
