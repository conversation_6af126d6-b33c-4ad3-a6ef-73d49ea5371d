<svg width="512" height="512" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background Circle with Gradient -->
  <circle cx="256" cy="256" r="256" fill="url(#background)"/>
  
  <!-- Main Palette Circle -->
  <circle cx="256" cy="256" r="200" fill="url(#paletteGradient)" stroke="url(#strokeGradient)" stroke-width="8"/>
  
  <!-- Color Dots on Palette -->
  <!-- Red -->
  <circle cx="256" cy="156" r="24" fill="#FF3366"/>
  <circle cx="256" cy="156" r="20" fill="#FF4577" opacity="0.8"/>
  
  <!-- Orange -->
  <circle cx="326" cy="186" r="24" fill="#FF6B35"/>
  <circle cx="326" cy="186" r="20" fill="#FF7D4A" opacity="0.8"/>
  
  <!-- Yellow -->
  <circle cx="356" cy="256" r="24" fill="#FFD700"/>
  <circle cx="356" cy="256" r="20" fill="#FFE033" opacity="0.8"/>
  
  <!-- Green -->
  <circle cx="326" cy="326" r="24" fill="#4CAF50"/>
  <circle cx="326" cy="326" r="20" fill="#66BB6A" opacity="0.8"/>
  
  <!-- Blue -->
  <circle cx="256" cy="356" r="24" fill="#2196F3"/>
  <circle cx="256" cy="356" r="20" fill="#42A5F5" opacity="0.8"/>
  
  <!-- Purple -->
  <circle cx="186" cy="326" r="24" fill="#9C27B0"/>
  <circle cx="186" cy="326" r="20" fill="#AB47BC" opacity="0.8"/>
  
  <!-- Pink -->
  <circle cx="156" cy="256" r="24" fill="#E91E63"/>
  <circle cx="156" cy="256" r="20" fill="#EC407A" opacity="0.8"/>
  
  <!-- Cyan -->
  <circle cx="186" cy="186" r="24" fill="#00BCD4"/>
  <circle cx="186" cy="186" r="20" fill="#26C6DA" opacity="0.8"/>
  
  <!-- Central Brush -->
  <g transform="translate(256, 256)">
    <!-- Brush Handle -->
    <rect x="-6" y="-40" width="12" height="80" rx="6" fill="url(#brushHandle)"/>
    
    <!-- Brush Ferrule (Metal Part) -->
    <rect x="-8" y="-45" width="16" height="15" rx="2" fill="url(#ferrule)"/>
    
    <!-- Brush Bristles -->
    <ellipse cx="0" cy="-52" rx="12" ry="8" fill="#8B4513"/>
    <ellipse cx="0" cy="-54" rx="10" ry="6" fill="#A0522D"/>
    
    <!-- Paint Drop -->
    <circle cx="15" cy="-35" r="6" fill="#FF3366" opacity="0.9"/>
    <circle cx="15" cy="-35" r="4" fill="#FF4577" opacity="0.7"/>
  </g>
  
  <!-- Sparkle Effects -->
  <g opacity="0.8">
    <!-- Large Sparkles -->
    <g transform="translate(150, 150)">
      <path d="M0,-8 L2,0 L0,8 L-2,0 Z" fill="#FFD700"/>
      <path d="M-8,0 L0,2 L8,0 L0,-2 Z" fill="#FFD700"/>
    </g>
    
    <g transform="translate(362, 150)">
      <path d="M0,-6 L1.5,0 L0,6 L-1.5,0 Z" fill="#FF3366"/>
      <path d="M-6,0 L0,1.5 L6,0 L0,-1.5 Z" fill="#FF3366"/>
    </g>
    
    <g transform="translate(150, 362)">
      <path d="M0,-6 L1.5,0 L0,6 L-1.5,0 Z" fill="#2196F3"/>
      <path d="M-6,0 L0,1.5 L6,0 L0,-1.5 Z" fill="#2196F3"/>
    </g>
    
    <g transform="translate(362, 362)">
      <path d="M0,-8 L2,0 L0,8 L-2,0 Z" fill="#4CAF50"/>
      <path d="M-8,0 L0,2 L8,0 L0,-2 Z" fill="#4CAF50"/>
    </g>
    
    <!-- Small Sparkles -->
    <circle cx="120" cy="200" r="3" fill="#FFD700" opacity="0.8"/>
    <circle cx="392" cy="200" r="3" fill="#FF3366" opacity="0.8"/>
    <circle cx="120" cy="312" r="3" fill="#2196F3" opacity="0.8"/>
    <circle cx="392" cy="312" r="3" fill="#4CAF50" opacity="0.8"/>
    <circle cx="200" cy="120" r="3" fill="#9C27B0" opacity="0.8"/>
    <circle cx="312" cy="120" r="3" fill="#00BCD4" opacity="0.8"/>
    <circle cx="200" cy="392" r="3" fill="#E91E63" opacity="0.8"/>
    <circle cx="312" cy="392" r="3" fill="#FF6B35" opacity="0.8"/>
  </g>
  
  <!-- Gradients -->
  <defs>
    <!-- Background Gradient -->
    <radialGradient id="background" cx="0.3" cy="0.3" r="1">
      <stop offset="0%" stop-color="#2A2A2A"/>
      <stop offset="100%" stop-color="#0A0A0A"/>
    </radialGradient>
    
    <!-- Palette Gradient -->
    <radialGradient id="paletteGradient" cx="0.3" cy="0.3" r="1">
      <stop offset="0%" stop-color="#FFFFFF"/>
      <stop offset="100%" stop-color="#F5F5F5"/>
    </radialGradient>
    
    <!-- Stroke Gradient -->
    <linearGradient id="strokeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#FF3366"/>
      <stop offset="50%" stop-color="#FF6B35"/>
      <stop offset="100%" stop-color="#FFD700"/>
    </linearGradient>
    
    <!-- Brush Handle Gradient -->
    <linearGradient id="brushHandle" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#8B4513"/>
      <stop offset="50%" stop-color="#A0522D"/>
      <stop offset="100%" stop-color="#8B4513"/>
    </linearGradient>
    
    <!-- Ferrule Gradient -->
    <linearGradient id="ferrule" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#C0C0C0"/>
      <stop offset="50%" stop-color="#E5E5E5"/>
      <stop offset="100%" stop-color="#C0C0C0"/>
    </linearGradient>
  </defs>
</svg>
