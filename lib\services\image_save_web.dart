import 'dart:typed_data';
import 'dart:html' as html;

/// Web-specific image save implementation
Future<bool> saveImagePlatform({
  required String imageName,
  required String originalImagePath,
  required Uint8List imageData,
  required Function(String) showMessage,
}) async {
  try {
    final blob = html.Blob([imageData]);
    final url = html.Url.createObjectUrlFromBlob(blob);
    final anchor = html.document.createElement('a') as html.AnchorElement
      ..href = url
      ..style.display = 'none'
      ..download = 'colored_${imageName}_${DateTime.now().millisecondsSinceEpoch}.png';
    html.document.body!.children.add(anchor);
    anchor.click();
    html.document.body!.children.remove(anchor);
    html.Url.revokeObjectUrl(url);

    showMessage('Image downloaded successfully!');
    return true;
  } catch (e) {
    showMessage('Error downloading image: $e');
    return false;
  }
}
