import 'package:flutter/material.dart';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';

class ModernColorPicker extends StatelessWidget {
  final Color selectedColor;
  final Function(Color) onColorChanged;

  const ModernColorPicker({
    super.key,
    required this.selectedColor,
    required this.onColorChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 80,
      child: Row(
        children: [
          // Predefined colors
          Expanded(
            child: ListView(
              scrollDirection: Axis.horizontal,
              children: [
                _buildColorButton(Colors.red),
                _buildColorButton(Colors.pink),
                _buildColorButton(Colors.purple),
                _buildColorButton(Colors.deepPurple),
                _buildColorButton(Colors.indigo),
                _buildColorButton(Colors.blue),
                _buildColorButton(Colors.lightBlue),
                _buildColorButton(Colors.cyan),
                _buildColorButton(Colors.teal),
                _buildColorButton(Colors.green),
                _buildColorButton(Colors.lightGreen),
                _buildColorButton(Colors.lime),
                _buildColorButton(Colors.yellow),
                _buildColorButton(Colors.amber),
                _buildColorButton(Colors.orange),
                _buildColorButton(Colors.deepOrange),
                _buildColorButton(Colors.brown),
                _buildColorButton(Colors.grey),
                _buildColorButton(Colors.blueGrey),
                _buildColorButton(Colors.black),
              ],
            ),
          ),
          
          const SizedBox(width: 8),
          
          // Custom color picker button
          GestureDetector(
            onTap: () => _showColorPicker(context),
            child: Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [
                    Colors.red,
                    Colors.yellow,
                    Colors.green,
                    Colors.cyan,
                    Colors.blue,
                    Colors.purple,
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(15),
                border: Border.all(color: Colors.grey[300]!, width: 2),
              ),
              child: const Icon(
                Icons.palette,
                color: Colors.white,
                size: 24,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildColorButton(Color color) {
    final isSelected = selectedColor == color;
    return GestureDetector(
      onTap: () => onColorChanged(color),
      child: Container(
        width: 50,
        height: 50,
        margin: const EdgeInsets.symmetric(horizontal: 4),
        decoration: BoxDecoration(
          color: color,
          shape: BoxShape.circle,
          border: Border.all(
            color: isSelected ? Colors.black : Colors.grey[300]!,
            width: isSelected ? 3 : 1,
          ),
          boxShadow: [
            if (isSelected)
              BoxShadow(
                color: color.withValues(alpha: 0.5),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
          ],
        ),
        child: isSelected
            ? const Icon(Icons.check, color: Colors.white, size: 20)
            : null,
      ),
    );
  }

  void _showColorPicker(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        Color tempColor = selectedColor;
        return AlertDialog(
          title: const Text(
            'Pick a Color',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          content: SingleChildScrollView(
            child: ColorPicker(
              pickerColor: selectedColor,
              onColorChanged: (Color color) {
                tempColor = color;
              },
              pickerAreaHeightPercent: 0.8,
              enableAlpha: false,
              displayThumbColor: true,
              paletteType: PaletteType.hsvWithHue,
              labelTypes: const [],
              hexInputBar: true,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                onColorChanged(tempColor);
                Navigator.of(context).pop();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFFF6B9D),
                foregroundColor: Colors.white,
              ),
              child: const Text('Select'),
            ),
          ],
        );
      },
    );
  }
}
