class ColoringImage {
  final String path;
  final String name;
  final String displayName;

  ColoringImage({
    required this.path,
    required this.name,
    required this.displayName,
  });

  /// Creates a ColoringImage from an asset path
  factory ColoringImage.fromAssetPath(String assetPath) {
    // Extract filename from path
    String fileName = assetPath.split('/').last;
    String nameWithoutExtension = fileName.split('.').first;
    
    // Convert snake_case or kebab-case to Title Case
    String displayName = nameWithoutExtension
        .replaceAll('_', ' ')
        .replaceAll('-', ' ')
        .split(' ')
        .map((word) => word.isNotEmpty 
            ? word[0].toUpperCase() + word.substring(1).toLowerCase()
            : '')
        .join(' ');

    return ColoringImage(
      path: assetPath,
      name: nameWithoutExtension,
      displayName: displayName,
    );
  }

  /// Gets the file extension
  String get extension => path.split('.').last.toLowerCase();

  /// Checks if the file is a valid image format
  bool get isValidImage {
    const validExtensions = ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'];
    return validExtensions.contains(extension);
  }

  @override
  String toString() {
    return 'ColoringImage(path: $path, name: $name, displayName: $displayName)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ColoringImage && other.path == path;
  }

  @override
  int get hashCode => path.hashCode;
}
