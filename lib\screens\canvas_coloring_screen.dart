
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'dart:ui' as ui;
import 'dart:typed_data';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';
import '../services/canvas_coloring_service.dart';
import '../services/image_save_service.dart';
import '../services/admob_service.dart';
import '../widgets/animated_brush.dart';
import '../widgets/banner_ad_widget.dart';

class CanvasColoringScreen extends StatefulWidget {
  final String imagePath;
  final String imageName;

  const CanvasColoringScreen({
    super.key,
    required this.imagePath,
    required this.imageName,
  });

  @override
  State<CanvasColoringScreen> createState() => _CanvasColoringScreenState();
}

// Drawing tool types
enum DrawingTool { bucket, brush, eraser }

class _CanvasColoringScreenState extends State<CanvasColoringScreen> {
  Color _selectedColor = Colors.red;
  DrawingTool _selectedTool = DrawingTool.bucket; // Default to bucket fill
  double _brushSize = 10.0; // Brush size for drawing tools
  final CanvasColoringService _coloringService = CanvasColoringService();

  // Drawing state
  bool _isDrawing = false;
  bool _isErasing = false;
  List<Offset> _currentStroke = [];

  // Undo/Redo functionality
  final List<ui.Image> _undoStack = [];
  final List<ui.Image> _redoStack = [];
  bool get _canUndo => _undoStack.isNotEmpty;
  bool get _canRedo => _redoStack.isNotEmpty;
  final GlobalKey _canvasKey = GlobalKey(); // Key to get exact canvas render box
  bool _isLoading = true;
  String? _error;
  bool _isProcessing = false;

  // Professional color palette like reference app
  final List<Color> _colors = [
    const Color(0xFF2196F3), // Blue
    const Color(0xFF9C27B0), // Purple
    const Color(0xFFE91E63), // Pink
    const Color(0xFFF44336), // Red
    const Color(0xFFFF9800), // Orange
    const Color(0xFFFFEB3B), // Yellow
    const Color(0xFF8BC34A), // Light Green
    const Color(0xFF4CAF50), // Green
    const Color(0xFF00BCD4), // Cyan
    const Color(0xFF795548), // Brown
  ];

  @override
  void initState() {
    super.initState();
    _loadImage();
  }

  /// Load and initialize image
  Future<void> _loadImage() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      await _coloringService.initialize(widget.imagePath);

      setState(() {
        _isLoading = false;
      });

      print('✅ Canvas coloring initialized: ${widget.imageName}');

    } catch (e) {
      setState(() {
        _isLoading = false;
        _error = 'Failed to load image: $e';
      });
      print('❌ Error loading image: $e');
    }
  }

  /// Handle tap on canvas for different tools
  Future<void> _handleCanvasTap(TapDownDetails details) async {
    if (_isLoading || _isProcessing || _coloringService.currentImage == null) return;

    // Get canvas coordinates
    final RenderBox? canvasBox = _canvasKey.currentContext?.findRenderObject() as RenderBox?;
    if (canvasBox == null) return;

    final globalPosition = details.globalPosition;
    final canvasLocalPosition = canvasBox.globalToLocal(globalPosition);
    final canvasSize = canvasBox.size;

    print('🎯 === CANVAS INTERACTION ===');
    print('🎯 Tool: $_selectedTool');
    print('🎯 Global tap: $globalPosition');
    print('🎯 Canvas local: $canvasLocalPosition');
    print('🎯 Canvas size: $canvasSize');

    // Handle different tools
    switch (_selectedTool) {
      case DrawingTool.bucket:
        await _performFloodFill(canvasLocalPosition, canvasSize);
        break;
      case DrawingTool.brush:
        _startDrawing(canvasLocalPosition, canvasSize);
        break;
      case DrawingTool.eraser:
        _startErasing(canvasLocalPosition, canvasSize);
        break;
    }
  }

  /// Perform flood fill operation
  Future<void> _performFloodFill(Offset position, Size canvasSize) async {
    try {
      HapticFeedback.lightImpact();
      setState(() {
        _isProcessing = true;
      });

      // Save current state for undo
      _saveStateForUndo();

      // Perform flood fill
      final ui.Image? newImage = await _coloringService.floodFill(
        position,
        _selectedColor,
        canvasSize,
      );

      if (newImage != null) {
        if (mounted) {
          setState(() {});
        }
        print('🎨 Flood fill completed successfully');
        HapticFeedback.lightImpact();
      }

    } catch (e) {
      print('❌ Error performing flood fill: $e');
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  /// Start drawing with brush
  void _startDrawing(Offset position, Size canvasSize) {
    if (_isProcessing) return;

    print('🖌️ Starting brush drawing at: $position');
    HapticFeedback.selectionClick();

    // Save state for undo
    _saveStateForUndo();

    // Start drawing stroke
    _isDrawing = true;
    _currentStroke = [position];

    _drawAtPosition(position, canvasSize);
  }

  /// Continue drawing with brush
  void _continueDrawing(Offset position, Size canvasSize) {
    if (!_isDrawing || _isProcessing) return;

    _currentStroke.add(position);
    _drawAtPosition(position, canvasSize);
  }

  /// End drawing stroke
  void _endDrawing() {
    if (_isDrawing) {
      print('🖌️ Ending brush stroke with ${_currentStroke.length} points');
      _isDrawing = false;
      _currentStroke.clear();
      HapticFeedback.selectionClick();
    }
  }

  /// Handle pan start for brush and eraser tools
  void _handlePanStart(DragStartDetails details, Size canvasSize) {
    if (_isProcessing) return;

    // Get canvas coordinates
    final RenderBox? canvasBox = _canvasKey.currentContext?.findRenderObject() as RenderBox?;
    if (canvasBox == null) return;

    final globalPosition = details.globalPosition;
    final canvasLocalPosition = canvasBox.globalToLocal(globalPosition);

    switch (_selectedTool) {
      case DrawingTool.brush:
        _startDrawing(canvasLocalPosition, canvasSize);
        break;
      case DrawingTool.eraser:
        _startErasing(canvasLocalPosition, canvasSize);
        break;
      case DrawingTool.bucket:
        // Bucket fill doesn't use pan gestures
        break;
    }
  }

  /// Handle pan update for brush and eraser tools
  void _handlePanUpdate(DragUpdateDetails details, Size canvasSize) {
    if (_isProcessing) return;

    // Get canvas coordinates
    final RenderBox? canvasBox = _canvasKey.currentContext?.findRenderObject() as RenderBox?;
    if (canvasBox == null) return;

    final globalPosition = details.globalPosition;
    final canvasLocalPosition = canvasBox.globalToLocal(globalPosition);

    switch (_selectedTool) {
      case DrawingTool.brush:
        _continueDrawing(canvasLocalPosition, canvasSize);
        break;
      case DrawingTool.eraser:
        _continueErasing(canvasLocalPosition, canvasSize);
        break;
      case DrawingTool.bucket:
        // Bucket fill doesn't use pan gestures
        break;
    }
  }

  /// Handle pan end for brush and eraser tools
  void _handlePanEnd() {
    switch (_selectedTool) {
      case DrawingTool.brush:
        _endDrawing();
        break;
      case DrawingTool.eraser:
        _endErasing();
        break;
      case DrawingTool.bucket:
        // Bucket fill doesn't use pan gestures
        break;
    }
  }

  /// Draw at specific position
  void _drawAtPosition(Offset position, Size canvasSize) async {
    try {
      final ui.Image? newImage = await _coloringService.drawBrush(
        position,
        _selectedColor,
        _brushSize,
        canvasSize,
      );

      if (newImage != null && mounted) {
        setState(() {});
      }
    } catch (e) {
      // Error drawing - silently handle
    }
  }

  /// Start erasing
  void _startErasing(Offset position, Size canvasSize) {
    if (_isProcessing) return;

    print('🧽 Starting eraser at: $position');
    HapticFeedback.selectionClick();

    // Save state for undo
    _saveStateForUndo();

    // Start erasing stroke
    _isErasing = true;
    _currentStroke = [position];

    _eraseAtPosition(position, canvasSize);
  }

  /// Continue erasing
  void _continueErasing(Offset position, Size canvasSize) {
    if (!_isErasing || _isProcessing) return;

    _currentStroke.add(position);
    _eraseAtPosition(position, canvasSize);
  }

  /// End erasing stroke
  void _endErasing() {
    if (_isErasing) {
      print('🧽 Ending eraser stroke with ${_currentStroke.length} points');
      _isErasing = false;
      _currentStroke.clear();
      HapticFeedback.selectionClick();
    }
  }

  /// Erase at specific position
  void _eraseAtPosition(Offset position, Size canvasSize) async {
    try {
      final ui.Image? newImage = await _coloringService.eraseArea(
        position,
        _brushSize,
        canvasSize,
      );

      if (newImage != null && mounted) {
        setState(() {});
      }
    } catch (e) {
      print('❌ Error erasing: $e');
    }
  }

  /// Reset to original image
  void _resetImage() {
    try {
      _coloringService.resetToOriginal();
      setState(() {});
      print('🔄 Image reset to original');
      HapticFeedback.mediumImpact();
    } catch (e) {
      print('❌ Error resetting image: $e');
    }
  }

  /// Convert image to pure black & white for better coloring
  Future<void> _convertToBW() async {
    if (_isProcessing) return;

    setState(() {
      _isProcessing = true;
    });

    try {
      final ui.Image? newImage = await _coloringService.convertToPureBW();
      if (newImage != null && mounted) {
        setState(() {});
        print('🎨 Image converted to pure B&W');
        HapticFeedback.mediumImpact();
      }
    } catch (e) {
      print('❌ Error converting to B&W: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  /// Build canvas content
  Widget _buildCanvasContent() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading image...'),
          ],
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, color: Colors.red, size: 48),
            const SizedBox(height: 16),
            Text(_error!, textAlign: TextAlign.center),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadImage,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    final ui.Image? image = _coloringService.currentImage;
    if (image == null) {
      return const Center(child: Text('No image available'));
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        // 🎯 RESPONSIVE CANVAS SIZE - maintains 5:6 aspect ratio
        final double maxWidth = constraints.maxWidth - 32; // Account for padding
        final double maxHeight = constraints.maxHeight - 32;

        // Calculate size maintaining 5:6 aspect ratio (like 500:600)
        double canvasWidth = maxWidth;
        double canvasHeight = canvasWidth * 1.2; // 6/5 = 1.2

        // If height is too big, scale down based on height
        if (canvasHeight > maxHeight) {
          canvasHeight = maxHeight;
          canvasWidth = canvasHeight / 1.2; // 5/6 = 0.833...
        }

        // Ensure minimum size for usability and maximum for performance
        canvasWidth = canvasWidth.clamp(300.0, 800.0);
        canvasHeight = canvasWidth * 1.2;

        return Container(
          width: canvasWidth,
          height: canvasHeight,
          decoration: BoxDecoration(
            border: Border.all(
              color: Colors.grey.shade300,
              width: 1,
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Stack(
              children: [
                // Canvas with image - RESPONSIVE SIZE
                GestureDetector(
                  onTapDown: _handleCanvasTap,
                  onPanStart: (details) => _handlePanStart(details, Size(canvasWidth, canvasHeight)),
                  onPanUpdate: (details) => _handlePanUpdate(details, Size(canvasWidth, canvasHeight)),
                  onPanEnd: (details) => _handlePanEnd(),
                  child: SizedBox(
                    key: _canvasKey, // 🎯 Key for exact coordinate conversion
                    width: canvasWidth,
                    height: canvasHeight,
                    child: CustomPaint(
                      painter: ResponsiveImagePainter(image),
                      size: Size(canvasWidth, canvasHeight),
                    ),
                  ),
                ),
        
              // Processing overlay with animated brush
              if (_isProcessing)
                Container(
                  color: Colors.black.withOpacity(0.3),
                  child: const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        AnimatedBrush(size: 64.0, color: Colors.white),
                        SizedBox(height: 16),
                        Text(
                          'Painting...',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Build improved professional toolbar with proper spacing
  Widget _buildProfessionalToolbar() {
    return Container(
      // Add padding for status bar + toolbar height
      padding: EdgeInsets.only(
        top: MediaQuery.of(context).padding.top, // Status bar height
      ),
      decoration: BoxDecoration(
        color: const Color(0xFF2C2C2C), // Dark gray like reference
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Container(
        height: 60, // Toolbar content height
        padding: const EdgeInsets.symmetric(horizontal: 8),
        child: Row(
          children: [
            // Back button with confirmation
            _buildCircularToolbarIcon(
              Icons.arrow_back,
              'Back to Gallery',
              _confirmBackToGallery,
            ),

            const Spacer(),

            // Save button
            _buildCircularToolbarIcon(
              Icons.save,
              'Save',
              _saveColoredImage,
            ),

            const SizedBox(width: 12),

            // Reset button with confirmation
            _buildCircularToolbarIcon(
              Icons.refresh,
              'Reset',
              _confirmReset,
            ),

            const SizedBox(width: 12),

            // Undo button
            _buildCircularToolbarIcon(
              Icons.undo,
              'Undo',
              _canUndo ? _performUndo : null,
              isEnabled: _canUndo,
            ),

            const SizedBox(width: 12),

            // Redo button
            _buildCircularToolbarIcon(
              Icons.redo,
              'Redo',
              _canRedo ? _performRedo : null,
              isEnabled: _canRedo,
            ),

            const SizedBox(width: 8),
          ],
        ),
      ),
    );
  }

  /// Build circular toolbar icon button
  Widget _buildCircularToolbarIcon(
    IconData icon,
    String tooltip,
    VoidCallback? onPressed, {
    bool isEnabled = true,
  }) {
    return Container(
      width: 44,
      height: 44,
      decoration: BoxDecoration(
        color: isEnabled
            ? const Color(0xFF404040)
            : const Color(0xFF2A2A2A), // Darker when disabled
        shape: BoxShape.circle, // Circular shape
        border: Border.all(
          color: isEnabled
              ? const Color(0xFF555555)
              : const Color(0xFF333333),
          width: 1,
        ),
      ),
      child: IconButton(
        icon: Icon(
          icon,
          color: isEnabled ? Colors.white : Colors.grey[600],
          size: 20,
        ),
        onPressed: onPressed,
        tooltip: tooltip,
        padding: EdgeInsets.zero,
      ),
    );
  }

  /// Confirm back to gallery with dialog
  void _confirmBackToGallery() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Leave Coloring?'),
        content: const Text('Are you sure you want to go back to the gallery? Any unsaved progress will be lost.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context); // Close dialog
              Navigator.pop(context); // Go back to gallery
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Leave'),
          ),
        ],
      ),
    );
  }

  /// Confirm reset with dialog
  void _confirmReset() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Image?'),
        content: const Text('Are you sure you want to reset the image to its original state? All coloring will be lost.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context); // Close dialog
              _resetImage(); // Perform reset
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }

  /// Perform undo operation
  void _performUndo() {
    if (_canUndo && _coloringService.currentImage != null) {
      // Save current state to redo stack
      _redoStack.add(_coloringService.currentImage!);

      // Restore previous state
      final previousImage = _undoStack.removeLast();
      _coloringService.setCurrentImage(previousImage);

      setState(() {});
      HapticFeedback.mediumImpact();
      print('🔄 Undo performed');
    }
  }

  /// Perform redo operation
  void _performRedo() {
    if (_canRedo) {
      // Save current state to undo stack
      if (_coloringService.currentImage != null) {
        _undoStack.add(_coloringService.currentImage!);
      }

      // Restore next state
      final nextImage = _redoStack.removeLast();
      _coloringService.setCurrentImage(nextImage);

      setState(() {});
      HapticFeedback.mediumImpact();
      print('🔄 Redo performed');
    }
  }

  /// Save current state for undo functionality
  void _saveStateForUndo() {
    if (_coloringService.currentImage != null) {
      _undoStack.add(_coloringService.currentImage!);
      _redoStack.clear(); // Clear redo stack when new action is performed

      // Limit undo stack size to prevent memory issues
      if (_undoStack.length > 20) {
        _undoStack.removeAt(0);
      }
    }
  }

  /// Save the colored image to device gallery
  void _saveColoredImage() async {
    if (_coloringService.currentImage == null) {
      _showMessage('No image to save');
      return;
    }

    try {
      // Show saving indicator
      setState(() {
        _isProcessing = true;
      });

      // Convert image to bytes
      final ByteData? byteData = await _coloringService.currentImage!.toByteData(
        format: ui.ImageByteFormat.png,
      );

      if (byteData == null) {
        _showMessage('Failed to process image');
        return;
      }

      final Uint8List pngBytes = byteData.buffer.asUint8List();

      // Use platform-specific save service
      await ImageSaveService.instance.saveImage(
        imageName: widget.imageName,
        originalImagePath: widget.imagePath,
        imageData: pngBytes,
        showMessage: _showMessage,
      );

      HapticFeedback.mediumImpact();
      print('🎨 Image saved successfully');

      // Show interstitial ad after successful save (reduced frequency)
      Future.delayed(const Duration(milliseconds: 1000), () {
        AdMobService.instance.showInterstitialAd(
          context: 'image_save_${widget.imageName}',
        );
      });

    } catch (e) {
      print('❌ Error saving image: $e');
      _showMessage('Failed to save image');
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  /// Show message to user
  void _showMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 2),
        backgroundColor: const Color(0xFF2196F3),
      ),
    );
  }





  /// Build professional color palette (Step 4)
  Widget _buildProfessionalColorPalette() {
    return Column(
      children: [
        // First row: Custom color picker + preset colors
        Row(
          children: [
            // Custom color picker (first item like reference)
            GestureDetector(
              onTap: _showColorPicker,
              child: Container(
                width: 50,
                height: 50,
                margin: const EdgeInsets.only(right: 8),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: const Color(0xFFE0E0E0), width: 2),
                  gradient: const LinearGradient(
                    colors: [
                      Color(0xFFFF0000), // Red
                      Color(0xFFFFFF00), // Yellow
                      Color(0xFF00FF00), // Green
                      Color(0xFF00FFFF), // Cyan
                      Color(0xFF0000FF), // Blue
                      Color(0xFFFF00FF), // Magenta
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
                child: const Icon(
                  Icons.palette,
                  color: Colors.white,
                  size: 24,
                ),
              ),
            ),

            // Preset colors in a scrollable row
            Expanded(
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: _colors.map((color) {
                    final isSelected = _selectedColor == color;
                    return GestureDetector(
                      onTap: () {
                        setState(() {
                          _selectedColor = color;
                        });
                        HapticFeedback.selectionClick();
                      },
                      child: Container(
                        width: 50,
                        height: 50,
                        margin: const EdgeInsets.only(right: 8),
                        decoration: BoxDecoration(
                          color: color,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: isSelected ? Colors.black : const Color(0xFFE0E0E0),
                            width: isSelected ? 3 : 2,
                          ),
                          boxShadow: isSelected
                              ? [
                                  BoxShadow(
                                    color: color.withOpacity(0.4),
                                    blurRadius: 8,
                                    spreadRadius: 2,
                                  ),
                                ]
                              : null,
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Show custom color picker dialog
  void _showColorPicker() {
    showDialog(
      context: context,
      builder: (context) {
        Color tempColor = _selectedColor;
        return AlertDialog(
          title: const Text('Choose Color'),
          content: SingleChildScrollView(
            child: ColorPicker(
              pickerColor: _selectedColor,
              onColorChanged: (color) {
                tempColor = color;
              },
              pickerAreaHeightPercent: 0.8,
              enableAlpha: false,
              displayThumbColor: true,
              paletteType: PaletteType.hsvWithHue,
              labelTypes: const [],
              hexInputBar: true,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _selectedColor = tempColor;
                });
                Navigator.pop(context);
                HapticFeedback.selectionClick();
              },
              child: const Text('Select'),
            ),
          ],
        );
      },
    );
  }

  /// Build merged tools and colors with separator
  Widget _buildMergedToolsAndColors() {
    return Row(
      children: [
        // 🖌️ Drawing Tools (Circular and smaller)
        _buildCompactDrawingTools(),

        // Separator line
        Container(
          width: 1,
          height: 50,
          margin: const EdgeInsets.symmetric(horizontal: 16),
          color: Colors.grey[300],
        ),

        // 🎨 Color Palette (Expanded)
        Expanded(
          child: _buildCompactColorPalette(),
        ),
      ],
    );
  }

  /// Build compact circular drawing tools
  Widget _buildCompactDrawingTools() {
    return Row(
      children: [
        _buildCompactToolIcon(
          DrawingTool.bucket,
          Icons.format_color_fill,
          'Fill',
        ),
        const SizedBox(width: 8),
        _buildCompactToolIcon(
          DrawingTool.brush,
          Icons.brush,
          'Brush',
        ),
        const SizedBox(width: 8),
        _buildCompactToolIcon(
          DrawingTool.eraser,
          Icons.auto_fix_high,
          'Eraser',
        ),
      ],
    );
  }

  /// Build compact tool icon (circular and smaller)
  Widget _buildCompactToolIcon(DrawingTool tool, IconData icon, String label) {
    final isSelected = _selectedTool == tool;
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedTool = tool;
        });
        HapticFeedback.selectionClick();
      },
      child: Container(
        width: 40, // Smaller size
        height: 40,
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFF2196F3) : Colors.grey[100],
          shape: BoxShape.circle, // Circular
          border: Border.all(
            color: isSelected ? const Color(0xFF2196F3) : Colors.grey[300]!,
            width: 2,
          ),
        ),
        child: Icon(
          icon,
          color: isSelected ? Colors.white : Colors.grey[600],
          size: 20, // Smaller icon
        ),
      ),
    );
  }

  /// Build compact color palette
  Widget _buildCompactColorPalette() {
    return Row(
      children: [
        // Custom color picker (first item)
        GestureDetector(
          onTap: _showColorPicker,
          child: Container(
            width: 40,
            height: 40,
            margin: const EdgeInsets.only(right: 8),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(color: const Color(0xFFE0E0E0), width: 2),
              gradient: const LinearGradient(
                colors: [
                  Color(0xFFFF0000), // Red
                  Color(0xFFFFFF00), // Yellow
                  Color(0xFF00FF00), // Green
                  Color(0xFF00FFFF), // Cyan
                  Color(0xFF0000FF), // Blue
                  Color(0xFFFF00FF), // Magenta
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            child: const Icon(
              Icons.palette,
              color: Colors.white,
              size: 20,
            ),
          ),
        ),

        // Preset colors in a scrollable row
        Expanded(
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: _colors.map((color) {
                final isSelected = _selectedColor == color;
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedColor = color;
                    });
                    HapticFeedback.selectionClick();
                  },
                  child: Container(
                    width: 40,
                    height: 40,
                    margin: const EdgeInsets.only(right: 8),
                    decoration: BoxDecoration(
                      color: color,
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: isSelected ? Colors.black : const Color(0xFFE0E0E0),
                        width: isSelected ? 3 : 2,
                      ),
                      boxShadow: isSelected
                          ? [
                              BoxShadow(
                                color: color.withOpacity(0.4),
                                blurRadius: 8,
                                spreadRadius: 2,
                              ),
                            ]
                          : null,
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
        ),
      ],
    );
  }

  /// Build brush size slider (Step 5)
  Widget _buildBrushSizeSlider() {
    return Row(
      children: [
        const Icon(Icons.brush, color: Color(0xFF666666), size: 20),
        const SizedBox(width: 12),
        Expanded(
          child: SliderTheme(
            data: SliderTheme.of(context).copyWith(
              activeTrackColor: const Color(0xFF2196F3),
              inactiveTrackColor: const Color(0xFFE0E0E0),
              thumbColor: const Color(0xFF2196F3),
              overlayColor: const Color(0xFF2196F3).withOpacity(0.2),
              thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 12),
              overlayShape: const RoundSliderOverlayShape(overlayRadius: 20),
            ),
            child: Slider(
              value: _brushSize,
              min: 2.0,
              max: 50.0,
              divisions: 24,
              label: '${_brushSize.round()}px',
              onChanged: (value) {
                setState(() {
                  _brushSize = value;
                });
                HapticFeedback.selectionClick();
              },
            ),
          ),
        ),
        const SizedBox(width: 12),
        Container(
          width: 40,
          height: 30,
          decoration: BoxDecoration(
            color: const Color(0xFFF5F5F5),
            borderRadius: BorderRadius.circular(6),
            border: Border.all(color: const Color(0xFFE0E0E0)),
          ),
          child: Center(
            child: Text(
              '${_brushSize.round()}',
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: Color(0xFF666666),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// Build drawing tools section (Step 3)
  Widget _buildDrawingTools() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // Bucket Fill Tool
          _buildDrawingTool(
            icon: Icons.format_color_fill,
            tool: DrawingTool.bucket,
            label: 'Fill',
          ),

          // Brush Tool
          _buildDrawingTool(
            icon: Icons.brush,
            tool: DrawingTool.brush,
            label: 'Brush',
          ),

          // Eraser Tool
          _buildDrawingTool(
            icon: Icons.auto_fix_high,
            tool: DrawingTool.eraser,
            label: 'Eraser',
          ),
        ],
      ),
    );
  }

  /// Build individual drawing tool button
  Widget _buildDrawingTool({
    required IconData icon,
    required DrawingTool tool,
    required String label,
  }) {
    final isSelected = _selectedTool == tool;

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedTool = tool;
        });
        HapticFeedback.selectionClick();
      },
      child: Container(
        width: 70,
        height: 70,
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFF2196F3) : const Color(0xFFF5F5F5),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? const Color(0xFF1976D2) : const Color(0xFFE0E0E0),
            width: 2,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: const Color(0xFF2196F3).withOpacity(0.3),
                    blurRadius: 8,
                    spreadRadius: 2,
                  ),
                ]
              : null,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: isSelected ? Colors.white : const Color(0xFF666666),
              size: 28,
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                color: isSelected ? Colors.white : const Color(0xFF666666),
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1E1E1E), // Dark background like reference
      body: Column(
        children: [
          // 📱 Professional Toolbar (Step 1)
          _buildProfessionalToolbar(),

          // Top banner ad (compact)
          const SectionBannerAd(section: 'canvas_top', compact: true),

          // 🖼️ Canvas Area (Step 2 - already good)
          Expanded(
            child: Container(
              width: double.infinity,
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: Center(
                  child: _buildCanvasContent(),
                ),
              ),
            ),
          ),

          // 🎨 Merged Tools and Colors Panel
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 4,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: Column(
              children: [
                // 🖌️ Drawing Tools + Colors (Merged with separator)
                _buildMergedToolsAndColors(),

                const SizedBox(height: 16),

                // 📏 Brush Size Slider (Step 5)
                _buildBrushSizeSlider(),
              ],
            ),
          ),
        ],
      ),
    );
  }


}

/// 🎯 FIXED 500x600 PAINTER - SCALES IMAGE TO FIT EXACTLY
class FixedSizeImagePainter extends CustomPainter {
  final ui.Image image;

  FixedSizeImagePainter(this.image);

  @override
  void paint(Canvas canvas, Size size) {
    // 🎯 FIXED CANVAS SIZE: 500x600
    const double canvasWidth = 500.0;
    const double canvasHeight = 600.0;

    // Scale image to fit exactly in 500x600 while maintaining aspect ratio
    final double imageAspectRatio = image.width / image.height;
    final double canvasAspectRatio = canvasWidth / canvasHeight;

    double scale;
    Offset offset;

    if (imageAspectRatio > canvasAspectRatio) {
      // Image is wider - fit to width
      scale = canvasWidth / image.width;
      offset = Offset(0, (canvasHeight - image.height * scale) / 2);
    } else {
      // Image is taller - fit to height
      scale = canvasHeight / image.height;
      offset = Offset((canvasWidth - image.width * scale) / 2, 0);
    }

    canvas.save();
    canvas.translate(offset.dx, offset.dy);
    canvas.scale(scale);
    canvas.drawImage(image, Offset.zero, Paint()..filterQuality = FilterQuality.high);
    canvas.restore();
  }

  @override
  bool shouldRepaint(FixedSizeImagePainter oldDelegate) {
    return oldDelegate.image != image;
  }
}

/// 🎯 RESPONSIVE PAINTER - SCALES IMAGE TO FIT ANY SIZE
class ResponsiveImagePainter extends CustomPainter {
  final ui.Image image;

  ResponsiveImagePainter(this.image);

  @override
  void paint(Canvas canvas, Size size) {
    // Get canvas dimensions
    final double canvasWidth = size.width;
    final double canvasHeight = size.height;

    // Scale image to fit exactly in canvas while maintaining aspect ratio
    final double imageAspectRatio = image.width / image.height;
    final double canvasAspectRatio = canvasWidth / canvasHeight;

    double scale;
    Offset offset;

    if (imageAspectRatio > canvasAspectRatio) {
      // Image is wider - fit to width
      scale = canvasWidth / image.width;
      offset = Offset(0, (canvasHeight - image.height * scale) / 2);
    } else {
      // Image is taller - fit to height
      scale = canvasHeight / image.height;
      offset = Offset((canvasWidth - image.width * scale) / 2, 0);
    }

    canvas.save();
    canvas.translate(offset.dx, offset.dy);
    canvas.scale(scale);
    canvas.drawImage(image, Offset.zero, Paint()..filterQuality = FilterQuality.high);
    canvas.restore();
  }

  @override
  bool shouldRepaint(ResponsiveImagePainter oldDelegate) {
    return oldDelegate.image != image;
  }
}
