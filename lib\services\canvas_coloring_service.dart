import 'dart:async';
import 'dart:collection';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image/image.dart' as img;

/// Professional Canvas-based Coloring Service
/// Uses bitmap flood fill algorithm for precise coloring
class CanvasColoringService {
  ui.Image? _originalImage;
  ui.Image? _currentImage;
  late int _imageWidth;
  late int _imageHeight;

  /// Get the current image for display
  ui.Image? get currentImage => _currentImage;

  /// Set the current image (for undo/redo functionality)
  void setCurrentImage(ui.Image image) {
    _currentImage = image;
  }

  /// Get the actual image size for coordinate debugging
  Size get imageSize {
    if (_currentImage == null) return Size.zero;
    return Size(_currentImage!.width.toDouble(), _currentImage!.height.toDouble());
  }
  
  /// Initialize with image path (PNG or generate from SVG name)
  Future<void> initialize(String imagePath) async {
    try {
      ui.Image image;

      if (imagePath.toLowerCase().endsWith('.png')) {
        // Load PNG image from assets
        final ByteData data = await rootBundle.load(imagePath);
        final Uint8List bytes = data.buffer.asUint8List();

        final ui.Codec codec = await ui.instantiateImageCodec(bytes);
        final ui.FrameInfo frameInfo = await codec.getNextFrame();
        image = frameInfo.image;
      } else {
        // Generate PNG from SVG name (temporary solution)
        image = await _generateImageFromName(imagePath);
      }

      _originalImage = image;
      _currentImage = image;
      _imageWidth = image.width;
      _imageHeight = image.height;

    } catch (e) {
      rethrow;
    }
  }

  /// Generate a coloring image based on the file name
  Future<ui.Image> _generateImageFromName(String imagePath) async {
    const int width = 400;
    const int height = 500;

    final ui.PictureRecorder recorder = ui.PictureRecorder();
    final Canvas canvas = Canvas(recorder);

    // White background
    canvas.drawRect(
      Rect.fromLTWH(0, 0, width.toDouble(), height.toDouble()),
      Paint()..color = const Color(0xFFFFFFFF),
    );

    final Paint linePaint = Paint()
      ..color = const Color(0xFF000000)
      ..strokeWidth = 4
      ..style = PaintingStyle.stroke;

    if (imagePath.contains('dragon')) {
      _drawDragon(canvas, linePaint);
    } else if (imagePath.contains('alien')) {
      _drawAlien(canvas, linePaint);
    } else {
      _drawFlower(canvas, linePaint);
    }

    final ui.Picture picture = recorder.endRecording();
    return await picture.toImage(width, height);
  }

  void _drawDragon(Canvas canvas, Paint paint) {
    // Dragon head
    canvas.drawCircle(const Offset(200, 150), 60, paint);
    // Dragon body
    canvas.drawOval(const Rect.fromLTWH(150, 200, 100, 150), paint);
    // Wings
    final Path leftWing = Path()
      ..moveTo(150, 220)
      ..quadraticBezierTo(80, 200, 100, 280)
      ..quadraticBezierTo(120, 320, 150, 300)
      ..close();
    canvas.drawPath(leftWing, paint);
    // Eyes
    canvas.drawCircle(const Offset(185, 140), 10, paint);
    canvas.drawCircle(const Offset(215, 140), 10, paint);
  }

  void _drawAlien(Canvas canvas, Paint paint) {
    // Alien head
    canvas.drawOval(const Rect.fromLTWH(150, 100, 100, 120), paint);
    // Antennae
    canvas.drawLine(const Offset(170, 100), const Offset(160, 60), paint);
    canvas.drawCircle(const Offset(160, 60), 10, paint);
    canvas.drawLine(const Offset(230, 100), const Offset(240, 60), paint);
    canvas.drawCircle(const Offset(240, 60), 10, paint);
    // Body
    canvas.drawOval(const Rect.fromLTWH(160, 210, 80, 120), paint);
    // Eyes
    canvas.drawCircle(const Offset(180, 150), 18, paint);
    canvas.drawCircle(const Offset(220, 150), 18, paint);
  }

  void _drawFlower(Canvas canvas, Paint paint) {
    // Flower center
    canvas.drawCircle(const Offset(200, 200), 30, paint);
    // Petals
    canvas.drawOval(Rect.fromCenter(center: const Offset(200, 140), width: 50, height: 80), paint);
    canvas.drawOval(Rect.fromCenter(center: const Offset(252, 170), width: 50, height: 80), paint);
    canvas.drawOval(Rect.fromCenter(center: const Offset(252, 230), width: 50, height: 80), paint);
    canvas.drawOval(Rect.fromCenter(center: const Offset(200, 260), width: 50, height: 80), paint);
    canvas.drawOval(Rect.fromCenter(center: const Offset(148, 230), width: 50, height: 80), paint);
    canvas.drawOval(Rect.fromCenter(center: const Offset(148, 170), width: 50, height: 80), paint);
    // Stem
    canvas.drawRect(const Rect.fromLTWH(190, 260, 20, 140), paint);
  }


  
  /// Enhanced flood fill with gap elimination at the given coordinates
  Future<ui.Image?> floodFill(Offset tapPosition, Color fillColor, Size widgetSize) async {
    if (_currentImage == null) return null;

    try {
      // Convert widget coordinates to image coordinates
      final imageCoords = _convertToImageCoordinates(tapPosition, widgetSize);

      // Check if coordinates are valid (not outside image display area)
      if (imageCoords.dx < 0 || imageCoords.dy < 0) {
        print('❌ Tap outside image display area - ignoring');
        return _currentImage;
      }

      final int x = imageCoords.dx.round();
      final int y = imageCoords.dy.round();

      // Check bounds
      if (x < 0 || x >= _imageWidth || y < 0 || y >= _imageHeight) {
        print('❌ Coordinates out of bounds: ($x, $y)');
        return _currentImage;
      }

      // Convert current image to bytes
      final ByteData? byteData = await _imageToBytes(_currentImage!);
      if (byteData == null) {
        return _currentImage;
      }

      // Get original color at tap position
      final ui.Color originalColor = _getPixelColor(byteData, x, y);

      // 🛡️ SMART BLACK LINE PROTECTION - Only ignore if clicking directly on black pixels
      // This allows filling white areas that are surrounded by black lines
      if (_isBlackLine(originalColor)) {
        print('🚫 Clicked directly on black line (${originalColor.r}, ${originalColor.g}, ${originalColor.b}) - protecting original artwork');
        return _currentImage;
      }

      print('✅ Clicked on fillable area (${originalColor.r}, ${originalColor.g}, ${originalColor.b}) - proceeding with fill');

      final ui.Color newColor = Color.fromARGB(
        (fillColor.a * 255.0).round() & 0xff,
        (fillColor.r * 255.0).round() & 0xff,
        (fillColor.g * 255.0).round() & 0xff,
        (fillColor.b * 255.0).round() & 0xff,
      );

      // Check if colors are different
      if (_colorsAreSimilar(originalColor, newColor)) {
        return _currentImage;
      }

      // Perform enhanced flood fill with dilation to eliminate gaps
      await _performEnhancedFloodFill(byteData, x, y, originalColor, newColor);

      // Convert back to image
      final ui.Image newImage = await _imageFromBytes(byteData, _imageWidth, _imageHeight);
      _currentImage = newImage;

      return newImage;

    } catch (e) {
      return _currentImage;
    }
  }
  
  /// Reset to original image
  ui.Image? resetToOriginal() {
    if (_originalImage != null) {
      _currentImage = _originalImage;
    }
    return _currentImage;
  }

  /// Convert current image to pure black & white for better coloring results
  /// This removes anti-aliasing and creates cleaner boundaries
  Future<ui.Image?> convertToPureBW() async {
    if (_currentImage == null) return null;

    try {
      final ByteData? byteData = await _imageToBytes(_currentImage!);
      if (byteData == null) return null;

      await _thresholdToPureBW(byteData);

      final ui.Image newImage = await _imageFromBytes(byteData, _imageWidth, _imageHeight);
      _currentImage = newImage;

      print('✅ Image converted to pure black & white');
      return newImage;
    } catch (e) {
      print('❌ Error converting to B&W: $e');
      return _currentImage;
    }
  }

  /// Draw brush stroke at the given position
  Future<ui.Image?> drawBrush(Offset position, Color color, double brushSize, Size canvasSize) async {
    if (_currentImage == null) return null;

    try {
      // Convert widget coordinates to image coordinates
      final imageCoords = _convertToImageCoordinates(position, canvasSize);

      // Check if coordinates are valid
      if (imageCoords.dx < 0 || imageCoords.dy < 0) {
        return _currentImage;
      }

      final int x = imageCoords.dx.round();
      final int y = imageCoords.dy.round();

      // Check bounds
      if (x < 0 || x >= _imageWidth || y < 0 || y >= _imageHeight) {
        return _currentImage;
      }

      // Convert current image to bytes
      final ByteData? byteData = await _imageToBytes(_currentImage!);
      if (byteData == null) return _currentImage;

      // Draw brush stroke
      await _drawBrushStroke(byteData, x, y, color, brushSize);

      // Convert back to image
      final ui.Image newImage = await _imageFromBytes(byteData, _imageWidth, _imageHeight);
      _currentImage = newImage;

      return newImage;
    } catch (e) {
      print('❌ Error drawing brush: $e');
      return _currentImage;
    }
  }

  /// Erase area at the given position
  Future<ui.Image?> eraseArea(Offset position, double brushSize, Size canvasSize) async {
    if (_currentImage == null) return null;

    try {
      // Convert widget coordinates to image coordinates
      final imageCoords = _convertToImageCoordinates(position, canvasSize);

      // Check if coordinates are valid
      if (imageCoords.dx < 0 || imageCoords.dy < 0) {
        return _currentImage;
      }

      final int x = imageCoords.dx.round();
      final int y = imageCoords.dy.round();

      // Check bounds
      if (x < 0 || x >= _imageWidth || y < 0 || y >= _imageHeight) {
        return _currentImage;
      }

      // Convert current image to bytes
      final ByteData? byteData = await _imageToBytes(_currentImage!);
      if (byteData == null) return _currentImage;

      // Erase area (paint with white)
      await _drawBrushStroke(byteData, x, y, const Color(0xFFFFFFFF), brushSize);

      // Convert back to image
      final ui.Image newImage = await _imageFromBytes(byteData, _imageWidth, _imageHeight);
      _currentImage = newImage;

      return newImage;
    } catch (e) {
      print('❌ Error erasing: $e');
      return _currentImage;
    }
  }
  
  /// 🎯 RESPONSIVE COORDINATE CONVERSION - MATCHES RESPONSIVE PAINTER EXACTLY
  Offset _convertToImageCoordinates(Offset widgetPoint, Size widgetSize) {
    print('🎯 === RESPONSIVE COORDINATE CONVERSION ===');
    print('🎯 Widget tap: $widgetPoint');
    print('🎯 Canvas size: $widgetSize');
    print('🎯 Image size: $_imageWidth x $_imageHeight');

    // 🎯 EXACT SAME LOGIC AS ResponsiveImagePainter
    final double canvasWidth = widgetSize.width;
    final double canvasHeight = widgetSize.height;

    final double imageAspectRatio = _imageWidth / _imageHeight;
    final double canvasAspectRatio = canvasWidth / canvasHeight;

    double scale;
    Offset offset;

    if (imageAspectRatio > canvasAspectRatio) {
      // Image is wider - fit to width
      scale = canvasWidth / _imageWidth;
      offset = Offset(0, (canvasHeight - _imageHeight * scale) / 2);
    } else {
      // Image is taller - fit to height
      scale = canvasHeight / _imageHeight;
      offset = Offset((canvasWidth - _imageWidth * scale) / 2, 0);
    }

    print('🎯 Scale: $scale, Offset: $offset');

    // Check if tap is within the actual image display area
    final double scaledWidth = _imageWidth * scale;
    final double scaledHeight = _imageHeight * scale;

    if (widgetPoint.dx < offset.dx || widgetPoint.dx > offset.dx + scaledWidth ||
        widgetPoint.dy < offset.dy || widgetPoint.dy > offset.dy + scaledHeight) {
      print('🚫 Tap outside image display area!');
      return Offset(-1, -1);
    }

    // Convert to image coordinates - reverse the painter's transformation
    final double imageX = (widgetPoint.dx - offset.dx) / scale;
    final double imageY = (widgetPoint.dy - offset.dy) / scale;

    print('🎯 Final image coordinates: ($imageX, $imageY)');
    print('🎯 ==========================================');

    return Offset(imageX, imageY);
  }
  
  /// Convert image to byte data
  Future<ByteData?> _imageToBytes(ui.Image image) async {
    try {
      return await image.toByteData(format: ui.ImageByteFormat.rawRgba);
    } catch (e) {
      return null;
    }
  }
  
  /// Convert byte data back to image
  Future<ui.Image> _imageFromBytes(ByteData bytes, int width, int height) {
    final Completer<ui.Image> completer = Completer();
    ui.decodeImageFromPixels(
      bytes.buffer.asUint8List(),
      width,
      height,
      ui.PixelFormat.rgba8888,
      (img) => completer.complete(img),
    );
    return completer.future;
  }
  
  /// Get pixel color at coordinates
  ui.Color _getPixelColor(ByteData bytes, int x, int y) {
    final int offset = (y * _imageWidth + x) * 4;
    final int r = bytes.getUint8(offset);
    final int g = bytes.getUint8(offset + 1);
    final int b = bytes.getUint8(offset + 2);
    final int a = bytes.getUint8(offset + 3);
    return ui.Color.fromARGB(a, r, g, b);
  }

  /// Set pixel color at coordinates
  void _setPixelColor(ByteData bytes, int x, int y, ui.Color color) {
    final int offset = (y * _imageWidth + x) * 4;
    bytes.setUint8(offset, (color.r * 255.0).round() & 0xff);
    bytes.setUint8(offset + 1, (color.g * 255.0).round() & 0xff);
    bytes.setUint8(offset + 2, (color.b * 255.0).round() & 0xff);
    bytes.setUint8(offset + 3, (color.a * 255.0).round() & 0xff);
  }

  /// Check if two colors are similar
  bool _colorsAreSimilar(ui.Color color1, ui.Color color2, {int threshold = 10}) {
    final int r1 = (color1.r * 255.0).round() & 0xff;
    final int g1 = (color1.g * 255.0).round() & 0xff;
    final int b1 = (color1.b * 255.0).round() & 0xff;
    final int a1 = (color1.a * 255.0).round() & 0xff;

    final int r2 = (color2.r * 255.0).round() & 0xff;
    final int g2 = (color2.g * 255.0).round() & 0xff;
    final int b2 = (color2.b * 255.0).round() & 0xff;
    final int a2 = (color2.a * 255.0).round() & 0xff;

    final int rDiff = (r1 - r2).abs();
    final int gDiff = (g1 - g2).abs();
    final int bDiff = (b1 - b2).abs();
    final int aDiff = (a1 - a2).abs();

    return rDiff < threshold && gDiff < threshold && bDiff < threshold && aDiff < threshold;
  }

  /// 🛡️ Check if color is a black line (original artwork)
  bool _isBlackLine(ui.Color color, {int threshold = 50}) {
    final int r = (color.r * 255.0).round() & 0xff;
    final int g = (color.g * 255.0).round() & 0xff;
    final int b = (color.b * 255.0).round() & 0xff;

    // More precise detection - only very dark colors are considered black lines
    return r < threshold && g < threshold && b < threshold;
  }

  /// Enhanced flood fill with dilation to eliminate gaps and dots
  Future<void> _performEnhancedFloodFill(
    ByteData bytes,
    int startX,
    int startY,
    ui.Color originalColor,
    ui.Color newColor,
  ) async {
    // Step 1: Perform standard flood fill with tolerance for anti-aliasing
    await _performFloodFillWithTolerance(bytes, startX, startY, originalColor, newColor);

    // Step 2: Apply dilation (expand filled areas by 1-2 pixels) to cover gaps
    await _applyDilation(bytes, newColor, expansionSize: 1);

    print('✅ Enhanced flood fill with dilation completed');
  }

  /// Flood fill with tolerance for anti-aliasing edges
  Future<void> _performFloodFillWithTolerance(
    ByteData bytes,
    int startX,
    int startY,
    ui.Color originalColor,
    ui.Color newColor,
  ) async {
    final Queue<Point> queue = Queue<Point>();
    final Set<String> visited = <String>{};

    queue.add(Point(startX, startY));

    int pixelsProcessed = 0;
    const int batchSize = 1000;
    const int tolerance = 15; // Higher tolerance for anti-aliasing

    while (queue.isNotEmpty) {
      final Point point = queue.removeFirst();
      final String key = '${point.x},${point.y}';

      if (visited.contains(key)) continue;
      visited.add(key);

      final int x = point.x;
      final int y = point.y;

      if (x < 0 || x >= _imageWidth || y < 0 || y >= _imageHeight) continue;

      final ui.Color currentColor = _getPixelColor(bytes, x, y);

      // Use higher tolerance for anti-aliasing but still protect black lines
      if (!_colorsAreSimilar(currentColor, originalColor, threshold: tolerance)) continue;
      if (_isBlackLine(currentColor) && !_isBlackLine(originalColor)) continue;

      _setPixelColor(bytes, x, y, newColor);

      // 8-directional fill for better coverage
      queue.add(Point(x + 1, y));
      queue.add(Point(x - 1, y));
      queue.add(Point(x, y + 1));
      queue.add(Point(x, y - 1));
      queue.add(Point(x + 1, y + 1));
      queue.add(Point(x - 1, y - 1));
      queue.add(Point(x + 1, y - 1));
      queue.add(Point(x - 1, y + 1));

      pixelsProcessed++;
      if (pixelsProcessed % batchSize == 0) {
        await Future.delayed(const Duration(microseconds: 1));
      }
    }
  }

  /// Apply dilation (expansion) to filled areas to eliminate gaps
  Future<void> _applyDilation(ByteData bytes, ui.Color fillColor, {int expansionSize = 1}) async {
    // Create a copy of current state to avoid modifying while reading
    final List<List<bool>> dilationMask = List.generate(
      _imageHeight,
      (y) => List.generate(_imageWidth, (x) => false),
    );

    // Find all pixels that match the fill color
    for (int y = 0; y < _imageHeight; y++) {
      for (int x = 0; x < _imageWidth; x++) {
        final ui.Color currentColor = _getPixelColor(bytes, x, y);
        if (_colorsAreSimilar(currentColor, fillColor, threshold: 5)) {
          // Mark surrounding pixels for dilation
          for (int dy = -expansionSize; dy <= expansionSize; dy++) {
            for (int dx = -expansionSize; dx <= expansionSize; dx++) {
              final int newX = x + dx;
              final int newY = y + dy;

              if (newX >= 0 && newX < _imageWidth && newY >= 0 && newY < _imageHeight) {
                final ui.Color neighborColor = _getPixelColor(bytes, newX, newY);
                // Only expand into white/light areas, not black lines
                if (!_isBlackLine(neighborColor)) {
                  dilationMask[newY][newX] = true;
                }
              }
            }
          }
        }
      }
    }

    // Apply the dilation mask
    for (int y = 0; y < _imageHeight; y++) {
      for (int x = 0; x < _imageWidth; x++) {
        if (dilationMask[y][x]) {
          _setPixelColor(bytes, x, y, fillColor);
        }
      }
    }
  }

  /// Draw brush stroke at the given coordinates
  Future<void> _drawBrushStroke(ByteData bytes, int centerX, int centerY, Color color, double brushSize) async {
    final ui.Color brushColor = Color.fromARGB(
      (color.a * 255.0).round() & 0xff,
      (color.r * 255.0).round() & 0xff,
      (color.g * 255.0).round() & 0xff,
      (color.b * 255.0).round() & 0xff,
    );

    final int radius = (brushSize / 2).round();

    // Draw circular brush
    for (int dy = -radius; dy <= radius; dy++) {
      for (int dx = -radius; dx <= radius; dx++) {
        final int x = centerX + dx;
        final int y = centerY + dy;

        // Check bounds
        if (x < 0 || x >= _imageWidth || y < 0 || y >= _imageHeight) continue;

        // Check if pixel is within circular brush
        final double distance = (dx * dx + dy * dy).toDouble();
        if (distance <= radius * radius) {
          // Don't paint over black lines unless we're erasing (white color)
          final ui.Color currentColor = _getPixelColor(bytes, x, y);
          final bool isWhite = brushColor.r == 1.0 && brushColor.g == 1.0 && brushColor.b == 1.0;
          if (!_isBlackLine(currentColor) || isWhite) {
            _setPixelColor(bytes, x, y, brushColor);
          }
        }
      }
    }
  }

  /// Convert image to pure black & white (optional preprocessing)
  Future<void> _thresholdToPureBW(ByteData bytes) async {
    for (int y = 0; y < _imageHeight; y++) {
      for (int x = 0; x < _imageWidth; x++) {
        final ui.Color currentColor = _getPixelColor(bytes, x, y);
        final double brightness = (currentColor.r + currentColor.g + currentColor.b) / 3.0;

        final ui.Color newColor = brightness < 0.5
          ? const ui.Color(0xFF000000) // Pure black
          : const ui.Color(0xFFFFFFFF); // Pure white

        _setPixelColor(bytes, x, y, newColor);
      }
    }
  }


}

/// Simple point class for flood fill algorithm
class Point {
  final int x;
  final int y;
  
  const Point(this.x, this.y);
  
  @override
  String toString() => '($x, $y)';
}
