<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ColorNest Professional Feature Graphic</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700;800&display=swap" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Poppins', sans-serif;
            background: #f8fafc;
        }

        .feature-graphic {
            width: 1024px;
            height: 500px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            position: relative;
            margin: 0 auto;
            border-radius: 0;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0,0,0,0.15);
        }
        
        .content-wrapper {
            display: flex;
            height: 100%;
            align-items: center;
            padding: 60px;
            box-sizing: border-box;
            position: relative;
        }

        .left-section {
            flex: 1.2;
            color: white;
            z-index: 10;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
        }

        .app-icon {
            width: 80px;
            height: 80px;
            background: url('android/app/src/main/res/mipmap-xxxhdpi/ic_launcher.png') center/cover;
            border-radius: 18px;
            margin-bottom: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
            border: 3px solid rgba(255,255,255,0.2);
        }

        .app-title {
            font-size: 56px;
            font-weight: 800;
            margin-bottom: 8px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
            color: white;
            letter-spacing: -1px;
        }

        .subtitle {
            font-size: 22px;
            margin-bottom: 25px;
            opacity: 0.95;
            font-weight: 300;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .features {
            list-style: none;
            padding: 0;
            margin: 0;
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
        }

        .feature-badge {
            background: rgba(255,255,255,0.15);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 500;
            color: white;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .feature-badge:before {
            content: "✨";
            font-size: 12px;
        }
        
        .right-section {
            flex: 1;
            position: relative;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            padding-right: 40px;
        }

        .device-showcase {
            position: relative;
            display: flex;
            align-items: center;
            gap: 30px;
        }

        .phone-mockup {
            width: 160px;
            height: 280px;
            background: linear-gradient(145deg, #2c3e50, #34495e);
            border-radius: 28px;
            padding: 8px;
            position: relative;
            transform: rotate(-8deg) translateY(-10px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.4);
            border: 2px solid rgba(255,255,255,0.1);
        }

        .tablet-mockup {
            width: 200px;
            height: 140px;
            background: linear-gradient(145deg, #2c3e50, #34495e);
            border-radius: 18px;
            padding: 6px;
            position: relative;
            transform: rotate(5deg) translateY(15px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.4);
            border: 2px solid rgba(255,255,255,0.1);
        }
        
        .screen {
            width: 100%;
            height: 100%;
            background: #1a1a1a;
            border-radius: 22px;
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .tablet-screen {
            background: #1a1a1a;
            border-radius: 14px;
        }

        .status-bar {
            height: 20px;
            background: rgba(255,255,255,0.05);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 10px;
            font-size: 8px;
            color: white;
        }

        .app-interface {
            flex: 1;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .coloring-preview {
            width: 60px;
            height: 60px;
            background: radial-gradient(circle, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
            border-radius: 12px;
            margin-bottom: 8px;
            animation: pulse 3s infinite;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .daily-bonus-badge {
            position: absolute;
            top: 30px;
            right: 60px;
            background: linear-gradient(135deg, #FFD700, #FFA500);
            color: #1a1a1a;
            padding: 12px 20px;
            border-radius: 30px;
            font-weight: 700;
            font-size: 16px;
            box-shadow: 0 8px 25px rgba(255, 215, 0, 0.5);
            animation: glow 3s infinite alternate;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .daily-bonus-badge:before {
            content: "🎁";
            font-size: 18px;
        }

        @keyframes glow {
            from {
                box-shadow: 0 8px 25px rgba(255, 215, 0, 0.5);
                transform: scale(1);
            }
            to {
                box-shadow: 0 12px 35px rgba(255, 215, 0, 0.8);
                transform: scale(1.02);
            }
        }

        .premium-badge {
            position: absolute;
            bottom: 30px;
            right: 60px;
            background: rgba(255,255,255,0.15);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            color: white;
            padding: 10px 18px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .premium-badge:before {
            content: "👑";
            font-size: 16px;
        }

        .app-interface-text {
            color: white;
            font-size: 10px;
            font-weight: 500;
            text-align: center;
            margin-top: 5px;
            opacity: 0.9;
        }
        
        .decorative-elements {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            overflow: hidden;
        }
        
        .floating-icon {
            position: absolute;
            font-size: 24px;
            opacity: 0.3;
            animation: float 6s ease-in-out infinite;
        }
        
        .floating-icon:nth-child(1) { top: 10%; left: 10%; animation-delay: 0s; }
        .floating-icon:nth-child(2) { top: 20%; right: 15%; animation-delay: 1s; }
        .floating-icon:nth-child(3) { bottom: 15%; left: 15%; animation-delay: 2s; }
        .floating-icon:nth-child(4) { bottom: 25%; right: 10%; animation-delay: 3s; }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        
        .controls {
            margin-top: 30px;
            text-align: center;
        }
        
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 0 10px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
        }
        
        .btn:hover {
            background: #5a6fd8;
        }
    </style>
</head>
<body>
    <div class="feature-graphic" id="featureGraphic">
        <div class="decorative-elements">
            <div class="floating-icon">🎨</div>
            <div class="floating-icon">🎨</div>
            <div class="floating-icon">✨</div>
            <div class="floating-icon">🎭</div>
        </div>
        
        <div class="daily-bonus-badge">Daily Bonus!</div>
        <div class="premium-badge">No Subscriptions</div>

        <div class="content-wrapper">
            <div class="left-section">
                <div class="app-icon"></div>
                <div class="app-title">ColorNest</div>
                <div class="subtitle">Premium Coloring Book Experience</div>
                <div class="features">
                    <div class="feature-badge">Daily Bonus System</div>
                    <div class="feature-badge">Ad-Unlock Premium</div>
                    <div class="feature-badge">Tablet Optimized</div>
                    <div class="feature-badge">Art Therapy</div>
                </div>
            </div>
            
            <div class="right-section">
                <div class="device-showcase">
                    <div class="phone-mockup">
                        <div class="screen">
                            <div class="status-bar">
                                <span>9:41</span>
                                <span>●●●</span>
                            </div>
                            <div class="app-interface">
                                <div class="coloring-preview"></div>
                                <div class="app-interface-text">ColorNest</div>
                            </div>
                        </div>
                    </div>

                    <div class="tablet-mockup">
                        <div class="screen tablet-screen">
                            <div class="status-bar">
                                <span>ColorNest</span>
                                <span>100%</span>
                            </div>
                            <div class="app-interface">
                                <div class="coloring-preview"></div>
                                <div class="app-interface-text">Tablet Experience</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <a href="#" class="download-btn">Download Now</a>
    </div>
    
    <div class="controls">
        <button class="btn" onclick="downloadImage()">Download as PNG (1024x500)</button>
        <button class="btn" onclick="changeTheme()">Change Theme</button>
    </div>
    
    <script>
        function downloadImage() {
            html2canvas(document.getElementById('featureGraphic'), {
                width: 1024,
                height: 500,
                scale: 1
            }).then(canvas => {
                const link = document.createElement('a');
                link.download = 'colornest-feature-graphic.png';
                link.href = canvas.toDataURL();
                link.click();
            });
        }
        
        function changeTheme() {
            const graphic = document.getElementById('featureGraphic');
            const themes = [
                'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
                'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
                'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)'
            ];
            const currentBg = graphic.style.background;
            const currentIndex = themes.indexOf(currentBg);
            const nextIndex = (currentIndex + 1) % themes.length;
            graphic.style.background = themes[nextIndex];
        }
    </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
</body>
</html>
