import 'dart:typed_data';
import 'saved_images_service.dart';

/// Mobile-specific image save implementation
Future<bool> saveImagePlatform({
  required String imageName,
  required String originalImagePath,
  required Uint8List imageData,
  required Function(String) showMessage,
}) async {
  try {
    final success = await SavedImagesService.instance.saveColoredImage(
      imageName: imageName,
      originalImagePath: originalImagePath,
      imageData: imageData,
    );
    
    if (success) {
      showMessage('Image saved to gallery!');
      return true;
    } else {
      showMessage('Failed to save image');
      return false;
    }
  } catch (e) {
    showMessage('Error saving image: $e');
    return false;
  }
}
