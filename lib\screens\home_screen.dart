import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'category_screen.dart';
import 'gallery_screen.dart';
import 'canvas_coloring_screen.dart';
import '../services/asset_service.dart';
import '../services/reward_service.dart';
import '../services/admob_service.dart';
import '../widgets/banner_ad_widget.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  List<CategoryData> _categories = [];
  bool _isLoading = true;
  bool _isDailyBonusAvailable = false;
  String _todaysBonusImage = '';
  bool _isTestMode = false;
  Duration? _timeUntilNextBonus;
  Timer? _countdownTimer;
  late AnimationController _dailyBonusController;
  late Animation<double> _dailyBonusAnimation;

  @override
  void initState() {
    super.initState();
    _loadCategories();
    _checkDailyBonus();

    // Initialize Daily Bonus animation
    _dailyBonusController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _dailyBonusAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _dailyBonusController,
      curve: Curves.easeInOut,
    ));

    // Start the animation and repeat
    _dailyBonusController.repeat(reverse: true);

    // Initialize ads
    Future.delayed(const Duration(seconds: 3), () {
      // Ads are automatically initialized in main.dart
    });
  }

  @override
  void dispose() {
    _countdownTimer?.cancel();
    _dailyBonusController.dispose();
    super.dispose();
  }

  Future<void> _checkDailyBonus() async {
    final rewardService = RewardService.instance;
    final isAvailable = await rewardService.isDailyBonusAvailable();
    final bonusImage = await rewardService.getTodaysBonusImage();
    final timeUntilNext = await rewardService.getTimeUntilNextBonus();

    setState(() {
      _isDailyBonusAvailable = isAvailable;
      _todaysBonusImage = bonusImage;
      _isTestMode = false; // No longer using test mode
      _timeUntilNextBonus = timeUntilNext;
    });

    // Start live timer if there's a countdown
    _startLiveTimer();
  }

  void _startLiveTimer() {
    _countdownTimer?.cancel();

    if (!_isDailyBonusAvailable && _timeUntilNextBonus != null) {
      _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) async {
        final rewardService = RewardService.instance;
        final timeUntilNext = await rewardService.getTimeUntilNextBonus();
        final isAvailable = await rewardService.isDailyBonusAvailable();

        setState(() {
          _timeUntilNextBonus = timeUntilNext;
          _isDailyBonusAvailable = isAvailable;
        });

        // Stop timer if bonus becomes available
        if (isAvailable || timeUntilNext == null) {
          timer.cancel();
        }
      });
    }
  }



  Future<void> _triggerAdForDailyBonus() async {
    HapticFeedback.lightImpact();

    final adMobService = AdMobService.instance;
    final rewardService = RewardService.instance;
    final bonusImage = await rewardService.getTodaysBonusImage();

    try {
      // Show rewarded ad for daily bonus
      final success = await adMobService.showRewardedAd(
        context: 'daily_bonus_$bonusImage',
        onRewardEarned: () async {
          // Claim the daily bonus
          await rewardService.claimDailyBonus();

          setState(() {
            _isDailyBonusAvailable = false;
          });

          // Reload categories to show newly unlocked premium image
          await _loadCategories();
        },
        onAdClosed: () async {
          // Navigate directly to coloring the claimed image
          if (mounted) {
            final imagePath = 'assets/images/premium/$bonusImage.png';

            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => CanvasColoringScreen(
                  imagePath: imagePath,
                  imageName: bonusImage,
                ),
              ),
            );
          }
        },
      );

      if (!success) {
        // Ad failed to show
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Ad failed to load. Please try again.'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      // Handle ad failure gracefully
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Ad failed to load. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _loadCategories() async {
    try {
      final defaultCategories = CategoryData.getDefaultCategories();
      final loadedCategories = <CategoryData>[];

      for (final category in defaultCategories) {
        final categoryWithImages = await CategoryData.withImages(category);
        // Only add categories that have images
        if (categoryWithImages.images.isNotEmpty) {
          loadedCategories.add(categoryWithImages);
        }
      }

      setState(() {
        _categories = loadedCategories;
        _isLoading = false;
      });
    } catch (e) {
      print('❌ Error loading categories: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1E1E1E),
      body: Column(
        children: [
          // Header with gradient
          _buildHeader(),

          // Top banner ad
          const SectionBannerAd(section: 'home_top'),

          // Navigation buttons (Create/Gallery)
          _buildNavigationButtons(),

          // Daily Bonus
          _buildDailyBonus(),

          // Categories Grid
          Expanded(
            child: _buildCategoriesGrid(),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.only(
        top: MediaQuery.of(context).padding.top + 12, // Reduced from 20
        bottom: 18, // Reduced from 30 (40% less)
        left: 20,
        right: 20,
      ),
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          colors: [Color(0xFFFF6B9D), Color(0xFF4ECDC4)],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.palette,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'ColorNest',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          const Text(
            "Let's make beautiful colors together!",
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w400,
            ),
          ),
          const SizedBox(height: 16),
          // Color dots
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildColorDot(const Color(0xFFFF6B9D)),
              _buildColorDot(const Color(0xFFFF8A5B)),
              _buildColorDot(const Color(0xFFFFD93D)),
              _buildColorDot(const Color(0xFF6BCF7F)),
              _buildColorDot(const Color(0xFF4ECDC4)),
              _buildColorDot(const Color(0xFF45B7D1)),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildColorDot(Color color) {
    return Container(
      width: 12,
      height: 12,
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: color,
        shape: BoxShape.circle,
      ),
    );
  }

  Widget _buildNavigationButtons() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          // Create button
          Expanded(
            child: GestureDetector(
              onTap: () {
                HapticFeedback.lightImpact();
                // Navigate to first category for now
                if (_categories.isNotEmpty) {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => CategoryScreen(category: _categories[0]),
                    ),
                  );
                }
              },
              child: Container(
                height: 60,
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFFFF6B9D), Color(0xFFFF8A5B)],
                  ),
                  borderRadius: BorderRadius.circular(30),
                ),
                child: const Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.palette, color: Colors.white, size: 24),
                    SizedBox(width: 8),
                    Text(
                      'Create',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          
          const SizedBox(width: 16),
          
          // Gallery button
          Expanded(
            child: GestureDetector(
              onTap: () {
                HapticFeedback.lightImpact();
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const GalleryScreen(),
                  ),
                );
              },
              child: Container(
                height: 60,
                decoration: BoxDecoration(
                  color: const Color(0xFF2A2A2A),
                  borderRadius: BorderRadius.circular(30),
                  border: Border.all(color: const Color(0xFF404040)),
                ),
                child: const Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.photo_library, color: Colors.white70, size: 24),
                    SizedBox(width: 8),
                    Text(
                      'Gallery',
                      style: TextStyle(
                        color: Colors.white70,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDailyBonus() {
    return Column(
      children: [
        // Main Daily Bonus Card
        AnimatedBuilder(
          animation: _dailyBonusAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: _dailyBonusAnimation.value,
              child: GestureDetector(
                onTap: _isDailyBonusAvailable ? _triggerAdForDailyBonus : null,
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 6),
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: _isDailyBonusAvailable
                        ? [const Color(0xFF6A1B9A), const Color(0xFF8E24AA), const Color(0xFFAB47BC)] // Purple gradient for valuable look
                        : [const Color(0xFF424242), const Color(0xFF616161)], // Gray when not available
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: _isDailyBonusAvailable ? [
                      BoxShadow(
                        color: const Color(0xFF6A1B9A).withOpacity(0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ] : null,
                  ),
                  child: Row(
                    children: [
                      Container(
                        width: 30,
                        height: 30,
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          _isDailyBonusAvailable ? Icons.diamond : Icons.schedule,
                          color: Colors.white,
                          size: 18,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _isDailyBonusAvailable
                                ? '💎 Premium Daily Bonus!'
                                : _timeUntilNextBonus != null
                                  ? '⏰ Next bonus in ${_timeUntilNextBonus!.inSeconds}s'
                                  : '⏰ Come back tomorrow!',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 2),
                            Text(
                              _isDailyBonusAvailable
                                ? "Watch ad to claim today's valuable image"
                                : _isTestMode
                                  ? "Test mode: 30 second intervals"
                                  : "Daily bonus resets every 24 hours",
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                      if (_isDailyBonusAvailable)
                        Container(
                          width: 24,
                          height: 24,
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.play_arrow,
                            color: Colors.white,
                            size: 14,
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),


      ],
    );
  }

  Widget _buildCategoriesGrid() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFFF6B9D)),
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.2,
        ),
        itemCount: _categories.length,
        itemBuilder: (context, index) {
          final category = _categories[index];
          return _buildCategoryCard(category);
        },
      ),
    );
  }

  Widget _buildCategoryCard(CategoryData category) {
    return GestureDetector(
      onTap: () async {
        HapticFeedback.lightImpact();

        // Show interstitial ad occasionally (user-friendly frequency)
        final adMobService = AdMobService.instance;
        await adMobService.showInterstitialAd(
          context: 'category_navigation_${category.name}',
        );

        // Navigate to category (whether ad showed or not)
        if (mounted) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => CategoryScreen(category: category),
            ),
          );
        }
      },
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Color(category.color),
              Color(category.color).withOpacity(0.8)
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              category.icon,
              style: const TextStyle(fontSize: 40),
            ),
            const SizedBox(height: 12),
            Text(
              category.displayName,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
