import 'package:flutter/material.dart';
import '../models/svg_models.dart';

/// Custom painter for rendering individual SVG paths with coloring support
class SvgPathPainter extends CustomPainter {
  const SvgPathPainter({
    required this.pathItem,
    required this.onTap,
  });

  final PathSvgItem pathItem;
  final VoidCallback onTap;

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint();
    
    // Set fill color (user's color or transparent)
    if (pathItem.fill != null) {
      paint.color = pathItem.fill!;
      paint.style = PaintingStyle.fill;
      canvas.drawPath(pathItem.path, paint);
    }

    // Always draw stroke for boundaries (line art)
    paint.color = Colors.black;
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = 3.0;
    paint.strokeJoin = StrokeJoin.round;
    paint.strokeCap = StrokeCap.round;
    canvas.drawPath(pathItem.path, paint);
  }

  @override
  bool? hitTest(Offset position) {
    // Check if the tap position is inside this path
    if (pathItem.path.contains(position)) {
      debugPrint('🎯 Hit detected on path: ${pathItem.id} at $position');
      onTap();
      return true; // Consume the hit
    }
    return false; // Let other paths handle the hit
  }

  @override
  bool shouldRepaint(SvgPathPainter oldDelegate) {
    return pathItem != oldDelegate.pathItem;
  }
}

/// Widget wrapper for SVG path painter
class SvgPathWidget extends StatelessWidget {
  const SvgPathWidget({
    super.key,
    required this.pathItem,
    required this.size,
    required this.onTap,
  });

  final PathSvgItem pathItem;
  final Size size;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      size: size,
      painter: SvgPathPainter(
        pathItem: pathItem,
        onTap: onTap,
      ),
    );
  }
}

/// Complete SVG coloring canvas that handles all paths
class SvgColoringCanvas extends StatelessWidget {
  const SvgColoringCanvas({
    super.key,
    required this.vectorImage,
    required this.onPathTapped,
  });

  final VectorImage vectorImage;
  final Function(String pathId) onPathTapped;

  @override
  Widget build(BuildContext context) {
    final Size canvasSize = vectorImage.size ?? const Size(400, 400);

    return Container(
      width: canvasSize.width,
      height: canvasSize.height,
      color: Colors.white,
      child: GestureDetector(
        onTapDown: (details) {
          final Offset localPosition = details.localPosition;
          debugPrint('🎯 Tap detected at: $localPosition');

          // Check which path was tapped
          for (final pathItem in vectorImage.items) {
            if (pathItem.path.contains(localPosition)) {
              debugPrint('✅ Hit path: ${pathItem.id}');
              onPathTapped(pathItem.id);
              break;
            }
          }
        },
        child: CustomPaint(
          size: canvasSize,
          painter: CompleteSvgPainter(vectorImage: vectorImage),
        ),
      ),
    );
  }
}

/// Painter that renders all SVG paths in one go
class CompleteSvgPainter extends CustomPainter {
  const CompleteSvgPainter({required this.vectorImage});

  final VectorImage vectorImage;

  @override
  void paint(Canvas canvas, Size size) {
    for (final pathItem in vectorImage.items) {
      final Paint paint = Paint();

      // Draw fill if user has colored this path
      if (pathItem.fill != null) {
        paint.color = pathItem.fill!;
        paint.style = PaintingStyle.fill;
        canvas.drawPath(pathItem.path, paint);
      }

      // Always draw stroke for boundaries (line art)
      paint.color = Colors.black;
      paint.style = PaintingStyle.stroke;
      paint.strokeWidth = 2.0;
      paint.strokeJoin = StrokeJoin.round;
      paint.strokeCap = StrokeCap.round;
      canvas.drawPath(pathItem.path, paint);
    }
  }

  @override
  bool shouldRepaint(CompleteSvgPainter oldDelegate) {
    return vectorImage != oldDelegate.vectorImage;
  }
}
