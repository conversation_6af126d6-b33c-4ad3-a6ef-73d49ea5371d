import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SavedImagesService {
  static SavedImagesService? _instance;
  static SavedImagesService get instance => _instance ??= SavedImagesService._();
  SavedImagesService._();

  static const String _savedImagesKey = 'saved_images';

  /// Save a colored image
  Future<bool> saveColoredImage({
    required String imageName,
    required String originalImagePath,
    required Uint8List imageData,
  }) async {
    try {
      // Get app documents directory
      final directory = await getApplicationDocumentsDirectory();
      final savedImagesDir = Directory('${directory.path}/saved_images');
      
      // Create directory if it doesn't exist
      if (!await savedImagesDir.exists()) {
        await savedImagesDir.create(recursive: true);
      }

      // Generate unique filename
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = '${imageName}_$timestamp.png';
      final filePath = '${savedImagesDir.path}/$fileName';

      // Save image file
      final file = File(filePath);
      await file.writeAsBytes(imageData);

      // Save metadata
      final savedImage = SavedImageData(
        id: timestamp.toString(),
        name: imageName,
        originalImagePath: originalImagePath,
        savedImagePath: filePath,
        savedAt: DateTime.now(),
      );

      await _addSavedImageMetadata(savedImage);

      print('✅ Image saved: $fileName');
      return true;
    } catch (e) {
      print('❌ Error saving image: $e');
      return false;
    }
  }

  /// Get all saved images
  Future<List<SavedImageData>> getSavedImages() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedImagesJson = prefs.getStringList(_savedImagesKey) ?? [];
      
      final savedImages = <SavedImageData>[];
      for (final imageJson in savedImagesJson) {
        try {
          final imageData = SavedImageData.fromJson(json.decode(imageJson));
          
          // Check if file still exists
          final file = File(imageData.savedImagePath);
          if (await file.exists()) {
            savedImages.add(imageData);
          } else {
            // Remove metadata for missing file
            await _removeSavedImageMetadata(imageData.id);
          }
        } catch (e) {
          print('❌ Error parsing saved image data: $e');
        }
      }

      // Sort by saved date (newest first)
      savedImages.sort((a, b) => b.savedAt.compareTo(a.savedAt));
      
      return savedImages;
    } catch (e) {
      print('❌ Error loading saved images: $e');
      return [];
    }
  }

  /// Delete a saved image
  Future<bool> deleteSavedImage(String imageId) async {
    try {
      final savedImages = await getSavedImages();
      final imageToDelete = savedImages.firstWhere(
        (img) => img.id == imageId,
        orElse: () => throw Exception('Image not found'),
      );

      // Delete file
      final file = File(imageToDelete.savedImagePath);
      if (await file.exists()) {
        await file.delete();
      }

      // Remove metadata
      await _removeSavedImageMetadata(imageId);

      print('✅ Image deleted: ${imageToDelete.name}');
      return true;
    } catch (e) {
      print('❌ Error deleting image: $e');
      return false;
    }
  }

  /// Add saved image metadata
  Future<void> _addSavedImageMetadata(SavedImageData imageData) async {
    final prefs = await SharedPreferences.getInstance();
    final savedImagesJson = prefs.getStringList(_savedImagesKey) ?? [];
    
    savedImagesJson.add(json.encode(imageData.toJson()));
    await prefs.setStringList(_savedImagesKey, savedImagesJson);
  }

  /// Remove saved image metadata
  Future<void> _removeSavedImageMetadata(String imageId) async {
    final prefs = await SharedPreferences.getInstance();
    final savedImagesJson = prefs.getStringList(_savedImagesKey) ?? [];
    
    savedImagesJson.removeWhere((imageJson) {
      try {
        final imageData = SavedImageData.fromJson(json.decode(imageJson));
        return imageData.id == imageId;
      } catch (e) {
        return false;
      }
    });
    
    await prefs.setStringList(_savedImagesKey, savedImagesJson);
  }

  /// Clear all saved images
  Future<void> clearAllSavedImages() async {
    try {
      // Delete all files
      final directory = await getApplicationDocumentsDirectory();
      final savedImagesDir = Directory('${directory.path}/saved_images');
      
      if (await savedImagesDir.exists()) {
        await savedImagesDir.delete(recursive: true);
      }

      // Clear metadata
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_savedImagesKey);

      print('✅ All saved images cleared');
    } catch (e) {
      print('❌ Error clearing saved images: $e');
    }
  }

  /// Get saved images count
  Future<int> getSavedImagesCount() async {
    final savedImages = await getSavedImages();
    return savedImages.length;
  }
}

/// Saved image data model
class SavedImageData {
  final String id;
  final String name;
  final String originalImagePath;
  final String savedImagePath;
  final DateTime savedAt;

  SavedImageData({
    required this.id,
    required this.name,
    required this.originalImagePath,
    required this.savedImagePath,
    required this.savedAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'originalImagePath': originalImagePath,
      'savedImagePath': savedImagePath,
      'savedAt': savedAt.toIso8601String(),
    };
  }

  factory SavedImageData.fromJson(Map<String, dynamic> json) {
    return SavedImageData(
      id: json['id'] as String,
      name: json['name'] as String,
      originalImagePath: json['originalImagePath'] as String,
      savedImagePath: json['savedImagePath'] as String,
      savedAt: DateTime.parse(json['savedAt'] as String),
    );
  }

  String get displayName {
    return name
        .split('_')
        .map((word) => word.isNotEmpty 
            ? word[0].toUpperCase() + word.substring(1).toLowerCase()
            : '')
        .join(' ');
  }

  String get formattedDate {
    final now = DateTime.now();
    final difference = now.difference(savedAt);
    
    if (difference.inDays == 0) {
      if (difference.inHours == 0) {
        return '${difference.inMinutes} minutes ago';
      }
      return '${difference.inHours} hours ago';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${savedAt.day}/${savedAt.month}/${savedAt.year}';
    }
  }
}
