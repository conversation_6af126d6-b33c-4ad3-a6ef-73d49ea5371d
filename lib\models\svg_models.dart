import 'dart:ui';

/// Represents a complete SVG coloring image with all fillable paths
class VectorImage {
  const VectorImage({
    required this.items,
    this.size,
  });

  final List<PathSvgItem> items;
  final Size? size;
}

/// Represents a single fillable path in the SVG with its current color
class PathSvgItem {
  const PathSvgItem({
    required this.path,
    required this.id,
    this.fill,
    this.originalFill,
  });

  final Path path;
  final String id;           // Unique identifier for the path (e.g., "cat-head", "left-ear")
  final Color? fill;         // Current fill color (null = transparent)
  final Color? originalFill; // Original fill color from SVG

  /// Create a copy with updated fill color
  PathSvgItem copyWith({
    Path? path,
    String? id,
    Color? fill,
    Color? originalFill,
  }) {
    return PathSvgItem(
      path: path ?? this.path,
      id: id ?? this.id,
      fill: fill ?? this.fill,
      originalFill: originalFill ?? this.originalFill,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PathSvgItem &&
        other.id == id &&
        other.fill == fill;
  }

  @override
  int get hashCode => id.hashCode ^ fill.hashCode;
}
