import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:xml/xml.dart';
import 'package:path_drawing/path_drawing.dart';

/// Professional SVG Coloring Service
/// Handles SVG parsing, path detection, and color modification
class SvgColoringService {
  late XmlDocument _svgDocument;
  late Map<String, ui.Path> _pathCache;
  late Map<String, String> _currentColors;
  late Size _svgSize;
  
  /// Initialize the service with SVG content
  Future<void> initialize(String svgContent) async {
    try {
      // Parse SVG XML
      _svgDocument = XmlDocument.parse(svgContent);
      _pathCache = {};
      _currentColors = {};
      
      // Extract SVG dimensions
      final svgElement = _svgDocument.findElements('svg').first;
      final viewBox = svgElement.getAttribute('viewBox');
      if (viewBox != null) {
        final parts = viewBox.split(' ');
        _svgSize = Size(double.parse(parts[2]), double.parse(parts[3]));
      } else {
        _svgSize = const Size(300, 400); // Default size
      }
      
      // Cache all colorable paths
      await _cachePaths();
      
      print('✅ SVG Coloring Service initialized');
      print('📐 SVG Size: ${_svgSize.width} x ${_svgSize.height}');
      print('🎨 Found ${_pathCache.length} colorable areas');
      
    } catch (e) {
      print('❌ Error initializing SVG: $e');
      rethrow;
    }
  }
  
  /// Cache all paths with IDs for hit testing
  Future<void> _cachePaths() async {
    final pathElements = _svgDocument.findAllElements('path');
    final circleElements = _svgDocument.findAllElements('circle');
    
    // Process path elements
    for (final element in pathElements) {
      final id = element.getAttribute('id');
      final pathData = element.getAttribute('d');
      final fill = element.getAttribute('fill');
      
      if (id != null && pathData != null && fill == 'white') {
        try {
          final path = parseSvgPathData(pathData);
          _pathCache[id] = path;
          _currentColors[id] = 'white';
          print('📍 Cached path: $id');
        } catch (e) {
          print('⚠️ Error parsing path $id: $e');
        }
      }
    }
    
    // Process circle elements
    for (final element in circleElements) {
      final id = element.getAttribute('id');
      final fill = element.getAttribute('fill');
      
      if (id != null && fill == 'white') {
        try {
          final cx = double.parse(element.getAttribute('cx') ?? '0');
          final cy = double.parse(element.getAttribute('cy') ?? '0');
          final r = double.parse(element.getAttribute('r') ?? '0');
          
          final path = ui.Path()
            ..addOval(Rect.fromCircle(center: Offset(cx, cy), radius: r));
          
          _pathCache[id] = path;
          _currentColors[id] = 'white';
          print('⭕ Cached circle: $id');
        } catch (e) {
          print('⚠️ Error parsing circle $id: $e');
        }
      }
    }
  }
  
  /// Detect which path contains the given point
  String? detectTappedPath(Offset localPoint, Size widgetSize) {
    try {
      // Convert widget coordinates to SVG coordinates
      final svgPoint = _convertToSvgCoordinates(localPoint, widgetSize);
      
      print('🎯 Tap detected at widget: $localPoint, SVG: $svgPoint');
      
      // Test each cached path
      for (final entry in _pathCache.entries) {
        final pathId = entry.key;
        final path = entry.value;
        
        if (path.contains(svgPoint)) {
          print('✅ Hit detected in path: $pathId');
          return pathId;
        }
      }
      
      print('❌ No path hit detected');
      return null;
      
    } catch (e) {
      print('❌ Error in path detection: $e');
      return null;
    }
  }
  
  /// Convert widget coordinates to SVG coordinates
  Offset _convertToSvgCoordinates(Offset widgetPoint, Size widgetSize) {
    final scaleX = _svgSize.width / widgetSize.width;
    final scaleY = _svgSize.height / widgetSize.height;
    
    return Offset(
      widgetPoint.dx * scaleX,
      widgetPoint.dy * scaleY,
    );
  }
  
  /// Color a specific path
  String colorPath(String pathId, Color color) {
    try {
      if (!_pathCache.containsKey(pathId)) {
        print('❌ Path not found: $pathId');
        return _svgDocument.toXmlString(pretty: true);
      }
      
      // Convert color to hex
      final colorHex = '#${color.value.toRadixString(16).substring(2)}';
      _currentColors[pathId] = colorHex;
      
      print('🎨 Coloring path $pathId with $colorHex');
      
      // Update the XML
      final updatedSvg = _updateSvgColors();
      
      return updatedSvg;
      
    } catch (e) {
      print('❌ Error coloring path: $e');
      return _svgDocument.toXmlString(pretty: true);
    }
  }
  
  /// Clear all colors (reset to white)
  String clearAllColors() {
    try {
      print('🧹 Clearing all colors');
      
      // Reset all colors to white
      for (final pathId in _currentColors.keys) {
        _currentColors[pathId] = 'white';
      }
      
      return _updateSvgColors();
      
    } catch (e) {
      print('❌ Error clearing colors: $e');
      return _svgDocument.toXmlString(pretty: true);
    }
  }
  
  /// Update SVG XML with current colors
  String _updateSvgColors() {
    try {
      // Clone the document to avoid modifying the original
      final xmlString = _svgDocument.toXmlString();
      final updatedDoc = XmlDocument.parse(xmlString);
      
      // Update path elements
      final pathElements = updatedDoc.findAllElements('path');
      for (final element in pathElements) {
        final id = element.getAttribute('id');
        if (id != null && _currentColors.containsKey(id)) {
          element.setAttribute('fill', _currentColors[id]!);
        }
      }
      
      // Update circle elements
      final circleElements = updatedDoc.findAllElements('circle');
      for (final element in circleElements) {
        final id = element.getAttribute('id');
        if (id != null && _currentColors.containsKey(id)) {
          element.setAttribute('fill', _currentColors[id]!);
        }
      }
      
      return updatedDoc.toXmlString(pretty: true);
      
    } catch (e) {
      print('❌ Error updating SVG colors: $e');
      return _svgDocument.toXmlString(pretty: true);
    }
  }
  
  /// Get current color of a path
  Color? getPathColor(String pathId) {
    final colorHex = _currentColors[pathId];
    if (colorHex == null || colorHex == 'white') return null;
    
    try {
      final hex = colorHex.replaceFirst('#', '');
      return Color(int.parse('FF$hex', radix: 16));
    } catch (e) {
      return null;
    }
  }
  
  /// Get all colorable path IDs
  List<String> get colorablePathIds => _pathCache.keys.toList();
  
  /// Get SVG size
  Size get svgSize => _svgSize;
}
