import 'dart:math';
import 'package:flutter/foundation.dart';

/// Ad test scenarios
enum AdTestScenario {
  success,           // Ad loads and completes successfully
  loadFailure,       // Ad fails to load
  watchFailure,      // Ad loads but fails during watching
  slowLoading,       // Ad takes longer to load
  quickSuccess,      // Ad loads and completes quickly
  userCanceled,      // User cancels ad before completion
}

/// Ad Test Service to simulate real ad scenarios for testing
class AdTestService {
  static final AdTestService _instance = AdTestService._internal();
  factory AdTestService() => _instance;
  AdTestService._internal();

  static AdTestService get instance => _instance;

  // Test configuration
  bool _isTestMode = true;
  AdTestScenario _currentScenario = AdTestScenario.success;
  int _adLoadTimeMs = 2000;
  int _adWatchTimeMs = 15000; // 15 seconds typical ad duration

  /// Configure test scenario
  void setTestScenario(AdTestScenario scenario) {
    _currentScenario = scenario;
    
    // Adjust timing based on scenario
    switch (scenario) {
      case AdTestScenario.success:
        _adLoadTimeMs = 2000;
        _adWatchTimeMs = 15000;
        break;
      case AdTestScenario.quickSuccess:
        _adLoadTimeMs = 500;
        _adWatchTimeMs = 5000;
        break;
      case AdTestScenario.slowLoading:
        _adLoadTimeMs = 8000;
        _adWatchTimeMs = 15000;
        break;
      case AdTestScenario.loadFailure:
      case AdTestScenario.watchFailure:
      case AdTestScenario.userCanceled:
        _adLoadTimeMs = 2000;
        _adWatchTimeMs = 8000; // Fail partway through
        break;
    }
    
    if (kDebugMode) {
      print('🎬 Ad Test: Set scenario to $_currentScenario');
      print('   Load time: ${_adLoadTimeMs}ms, Watch time: ${_adWatchTimeMs}ms');
    }
  }

  /// Enable/disable test mode
  void setTestMode(bool enabled) {
    _isTestMode = enabled;
    if (kDebugMode) {
      print('🎬 Ad Test: Test mode ${enabled ? 'ENABLED' : 'DISABLED'}');
    }
  }

  /// Get current test scenario
  AdTestScenario get currentScenario => _currentScenario;
  bool get isTestMode => _isTestMode;

  /// Simulate ad loading and watching process
  Future<AdTestResult> simulateRewardedAd({
    required String imageName,
    required String category,
    Function(String)? onStatusUpdate,
  }) async {
    if (!_isTestMode) {
      throw Exception('Ad test service is disabled. Enable test mode first.');
    }

    if (kDebugMode) {
      print('🎬 Ad Test: Starting simulation for $category/$imageName');
      print('   Scenario: $_currentScenario');
    }

    try {
      // Phase 1: Ad Loading
      onStatusUpdate?.call('Loading ad...');
      await _simulateAdLoading();

      // Check for load failure
      if (_currentScenario == AdTestScenario.loadFailure) {
        return AdTestResult(
          success: false,
          error: 'Failed to load ad. Please check your internet connection.',
          phase: AdTestPhase.loading,
        );
      }

      // Phase 2: Ad Watching
      onStatusUpdate?.call('Playing ad...');
      await _simulateAdWatching(onStatusUpdate);

      // Check for watch failure
      if (_currentScenario == AdTestScenario.watchFailure) {
        return AdTestResult(
          success: false,
          error: 'Ad playback failed. Please try again.',
          phase: AdTestPhase.watching,
        );
      }

      // Check for user cancellation
      if (_currentScenario == AdTestScenario.userCanceled) {
        return AdTestResult(
          success: false,
          error: 'Ad was canceled before completion.',
          phase: AdTestPhase.watching,
          userCanceled: true,
        );
      }

      // Phase 3: Success
      onStatusUpdate?.call('Ad completed successfully!');
      
      if (kDebugMode) {
        print('🎬 Ad Test: SUCCESS - Reward earned for $category/$imageName');
      }

      return AdTestResult(
        success: true,
        rewardEarned: true,
        phase: AdTestPhase.completed,
      );

    } catch (e) {
      if (kDebugMode) {
        print('🎬 Ad Test: ERROR - $e');
      }
      
      return AdTestResult(
        success: false,
        error: 'Unexpected error: $e',
        phase: AdTestPhase.error,
      );
    }
  }

  /// Simulate ad loading phase
  Future<void> _simulateAdLoading() async {
    final random = Random();
    
    // Add some randomness to loading time (±20%)
    final variance = (_adLoadTimeMs * 0.2).round();
    final actualLoadTime = _adLoadTimeMs + random.nextInt(variance * 2) - variance;
    
    await Future.delayed(Duration(milliseconds: actualLoadTime));
  }

  /// Simulate ad watching phase with progress updates
  Future<void> _simulateAdWatching(Function(String)? onStatusUpdate) async {
    final totalDuration = _adWatchTimeMs;
    final updateInterval = 1000; // Update every second
    final totalSteps = totalDuration ~/ updateInterval;

    for (int step = 0; step < totalSteps; step++) {
      await Future.delayed(Duration(milliseconds: updateInterval));
      
      final progress = ((step + 1) / totalSteps * 100).round();
      onStatusUpdate?.call('Playing ad... ${progress}%');

      // Simulate failure partway through for certain scenarios
      if ((_currentScenario == AdTestScenario.watchFailure && step > totalSteps * 0.6) ||
          (_currentScenario == AdTestScenario.userCanceled && step > totalSteps * 0.4)) {
        break;
      }
    }
  }


}

/// Result of ad test simulation
class AdTestResult {
  final bool success;
  final bool rewardEarned;
  final String? error;
  final AdTestPhase phase;
  final bool userCanceled;

  AdTestResult({
    required this.success,
    this.rewardEarned = false,
    this.error,
    required this.phase,
    this.userCanceled = false,
  });

  @override
  String toString() {
    return 'AdTestResult(success: $success, reward: $rewardEarned, phase: $phase, error: $error)';
  }
}

/// Phases of ad testing
enum AdTestPhase {
  loading,
  watching,
  completed,
  error,
}
